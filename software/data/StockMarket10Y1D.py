from data.base_data import BaseDataLoader
import pandas as pd
from datetime import datetime, timedelta
import requests
from utils.console_output import ConsoleOutput


class StockMarket10Y1D(BaseDataLoader):
    """
    Loads 10 years of daily stock market data from Alpaca API.
    """

    def __init__(self, ticker="AAPL"):
        super().__init__()
        self.ticker = ticker
        self.datetime_col = "Date"
        self.additional_columns = {
            col: lambda s: pd.to_numeric(s, errors="coerce").notna().all()
            for col in ["Open", "High", "Low", "Close", "Volume"]
        }

        self.api_key = self.get_api_key("StockMarket10Y1D", "API_KEY_ID")
        self.secret_key = self.get_api_key("StockMarket10Y1D", "SECRET_KEY")
        self.base_url = "https://data.alpaca.markets/v2/stocks/bars"

    def load_historical_data(self) -> pd.DataFrame:
        """
        Loads historical stock data for the past 10 years at daily intervals.
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=3650)

        try:
            return self._fetch_from_alpaca(start_date, end_date)
        except Exception as e:
            ConsoleOutput.print_error(f"[StockMarket10Y1D] Error: {str(e)}")
            return self._prepare_empty_df()

    def _fetch_from_alpaca(self, start_date, end_date) -> pd.DataFrame:
        """
        Fetch data from Alpaca API using direct HTTP request
        """
        params = {
            "symbols": self.ticker,
            "timeframe": "1D",
            "start": start_date.strftime("%Y-%m-%d"),
            "end": end_date.strftime("%Y-%m-%d"),
            "adjustment": "split",
            "feed": "iex",
            "sort": "asc",
        }

        headers = {
            "accept": "application/json",
            "APCA-API-KEY-ID": self.api_key,
            "APCA-API-SECRET-KEY": self.secret_key,
        }

        response = requests.get(self.base_url, headers=headers, params=params)

        if response.status_code != 200:
            raise Exception(
                f"API request failed with status code {response.status_code}: {response.text}"
            )

        data = response.json()

        if (
            not data
            or "bars" not in data
            or self.ticker not in data["bars"]
            or not data["bars"][self.ticker]
        ):
            return self._prepare_empty_df()

        # Convert to DataFrame
        df = pd.DataFrame(data["bars"][self.ticker])

        # Process DataFrame
        df["t"] = pd.to_datetime(df["t"])
        df = df.set_index("t")
        df.index.name = self.datetime_col

        # Rename columns to standard format
        df = df.rename(
            columns={"o": "Open", "h": "High", "l": "Low", "c": "Close", "v": "Volume"}
        )

        # Drop invalid rows and ensure timezone-naive
        df = df.dropna(subset=["Open", "High", "Low", "Close"])
        df = self._ensure_timezone_naive(df)

        # Get only the required columns
        df = df[["Open", "High", "Low", "Close", "Volume"]]

        try:
            self._validate_data(df)
        except ValueError:
            pass

        return df
