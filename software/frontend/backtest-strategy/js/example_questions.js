/**
 * Example Business Questions for Backtest Strategy
 *
 * This file contains example business questions that users can select
 * to quickly populate the business question input field.
 */

const exampleBusinessQuestions = [
    // RSI & Oscillator Strategies
    "Backtest a strategy that buys Meta (META) when the RSI is below 30 and sells when it's above 70 over the past year.",
    "Test a Stochastic RSI strategy for Tesla (TSLA) that buys when %K crosses above %D below 30 and sells when it crosses below above 70.",
    "Evaluate a Williams %R strategy for Apple (AAPL) that buys when Williams %R is below -80 and sells when it rises above -20.",
    "Backtest a CCI (Commodity Channel Index) strategy for Microsoft (MSFT) that buys when CCI drops below -100 and sells when it rises above +100.",

    // Moving Average Strategies
    "Evaluate the performance of a moving average crossover strategy on Amazon (AMZN) with a 50-day and 200-day moving average over the past 2 years.",
    "Test an EMA crossover strategy for Alphabet (GOOGL) using 12-day and 26-day exponential moving averages with volume confirmation.",
    "Evaluate a triple moving average strategy for JPMorgan Chase (JPM) using 5-day, 20-day, and 50-day moving averages to identify trend changes.",

    // Bollinger Bands & Volatility
    "Backtest a Bollinger Bands strategy on Tesla (TSLA) by buying when the price touches the lower band and selling when it touches the upper band over the past 6 months.",
    "Test a Bollinger Bands squeeze strategy for Netflix (NFLX) that buys when volatility contracts and price breaks out of the bands.",

    // MACD Strategies
    "Backtest a MACD strategy for Apple (AAPL) that buys when the MACD line crosses above the signal line and sells when it crosses below.",
    "Test a MACD histogram strategy for Advanced Micro Devices (AMD) that buys when the histogram turns positive and sells when it turns negative.",

    // Momentum & Trend Following
    "Test a momentum strategy for Nvidia (NVDA) that buys when the 14-day ROC (Rate of Change) is above 5% and sells when it falls below -3%.",
    "Backtest an ADX trend strategy for Salesforce (CRM) that only trades when ADX is above 25 and follows the directional movement indicators.",
    "Evaluate a Parabolic SAR strategy for Coca-Cola (KO) that buys when price crosses above the SAR and sells when it crosses below.",

    // Mean Reversion Strategies
    "Evaluate a mean reversion strategy for Alphabet (GOOGL) that buys when the price is 2 standard deviations below the 20-day moving average and sells when it returns to the mean.",
    "Test a Z-Score mean reversion strategy for Walt Disney (DIS) that buys when the Z-Score is below -2 and sells when it's above +2.",
    "Backtest an RSI oversold bounce strategy for NIO that buys when the 2-day RSI drops below 15 and sells when it rises above 85, targeting mean reversion in this underperforming stock.",

    // Breakout & Channel Strategies
    "Test a strategy for Microsoft (MSFT) that buys when the price breaks above the 20-day high and sells after a 5% gain or 3% loss.",
    "Backtest a breakout strategy for Advanced Micro Devices (AMD) that buys when the price breaks out of a 30-day price channel with increased volume.",
    "Evaluate a Donchian Channel strategy for Ford Motor (F) that buys on upper channel breakouts and sells on lower channel breakouts.",

    // Volume-Based Strategies
    "Backtest a volume-based strategy for Netflix (NFLX) that buys when the volume spikes 50% above the 10-day average volume and the price increases.",
    "Test a volume-price trend strategy for PayPal (PYPL) that buys when VPT indicator shows accumulation and price confirms the trend.",

    // Multi-Indicator Strategies
    "Test a strategy for Nvidia (NVDA) that combines RSI and MACD indicators, buying when both are showing bullish signals and selling when either shows bearish signals.",
    "Backtest a triple confirmation strategy for Amazon (AMZN) using RSI, MACD, and Bollinger Bands that requires all three to align before entering trades.",
    "Evaluate a momentum cocktail strategy for Apple (AAPL) combining RSI, Stochastic, and Williams %R that buys when all three are oversold.",

    // Gap & Price Action Strategies
    "Test a gap trading strategy for Tesla (TSLA) that buys on gap-ups above 2% and sells when the gap fills or after 3 days.",
    "Backtest a support/resistance strategy for Microsoft (MSFT) that buys at key support levels and sells at resistance levels.",

    // Volatility Strategies
    "Evaluate a volatility breakout strategy for SPDR S&P 500 ETF (SPY) that buys when daily volatility exceeds the 20-day average by 50%.",
    "Test an ATR-based strategy for Invesco QQQ Trust (QQQ) that adjusts position size based on Average True Range and trades trend breakouts."
];
