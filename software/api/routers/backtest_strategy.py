from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
from typing import Dict, Any, Optional, List, Tuple
from pydantic import BaseModel, Field
import logging
import uuid
import asyncio
import re
import os
import sys
from io import StringIO
import traceback
import pandas as pd
import numpy as np
from itertools import product
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from software.ai.llm.llm_connect import get_llm_connect
from data import registry
from main import StockAnalysisApp
from software.db.backtest_strategy_eval_repository import BacktestStrategyEvalRepository

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

# In-memory storage for task status (will be replaced with database in production)
tasks = {}

class TickerSelection(BaseModel):
    """Schema for ticker selection from business question"""
    ticker: str = Field(
        ...,
        description="The ticker to select from the business question",
        examples=["META", "AMZN", "AMD", "JPM", "MSFT"]
    )



class StrategyMethods(BaseModel):
    """Schema for AI-generated strategy methods"""

    # Technical indicators calculation
    calculate_indicators_code: str = Field(
        ...,
        description="Python code for calculate_indicators() method body. Should calculate all technical indicators needed for the strategy (RSI, MACD, Bollinger Bands, etc.). Include proper error handling and data validation.",
        examples=[
            "if len(self.df) < 50:\n    return\nself.df['rsi'] = ta.rsi(self.df['Close'], length=14)\nself.df['sma_20'] = self.df['Close'].rolling(20).mean()\nself.df['sma_50'] = self.df['Close'].rolling(50).mean()"
        ]
    )

    # Entry/exit logic
    should_long_code: str = Field(
        ...,
        description="Python code for should_long() method body. Return True when entering long position. Include data validation.",
        examples=[
            "if len(self.df) < 50 or pd.isna(self.df['rsi'].iloc[-1]):\n    return False\nreturn self.df['rsi'].iloc[-1] < 30 and self.df['Close'].iloc[-1] > self.df['sma_20'].iloc[-1]"
        ]
    )

    should_exit_long_code: str = Field(
        ...,
        description="Python code for should_exit_long() method body. Return True when exiting long position. Include data validation.",
        examples=[
            "if len(self.df) < 50 or pd.isna(self.df['rsi'].iloc[-1]):\n    return False\nreturn self.df['rsi'].iloc[-1] > 70"
        ]
    )

    should_short_code: Optional[str] = Field(
        None,
        description="Python code for should_short() method body. Return True when entering short position. Include data validation."
    )

    should_exit_short_code: Optional[str] = Field(
        None,
        description="Python code for should_exit_short() method body. Return True when exiting short position. Include data validation."
    )

    # Metadata for chart generation
    indicators_for_chart: List[str] = Field(
        ...,
        description="List of indicator column names to display in Panel 2 of the chart",
        examples=[["rsi", "macd", "sma_20", "bollinger_upper", "bollinger_lower"]]
    )



class IndicatorRange(BaseModel):
    """Range configuration for indicator parameters"""
    name: str = Field(..., description="Parameter name (e.g., 'rsi_period', 'sma_period')")
    base_value: float = Field(..., description="Default/base value for the parameter")
    min_value: float = Field(..., description="Minimum value for optimization")
    max_value: float = Field(..., description="Maximum value for optimization")
    step: float = Field(..., description="Step size for parameter sweep")

class ThresholdRange(BaseModel):
    """Range configuration for strategy thresholds"""
    name: str = Field(..., description="Threshold name (e.g., 'oversold_threshold', 'overbought_threshold')")
    base_value: float = Field(..., description="Default/base value for the threshold")
    min_value: float = Field(..., description="Minimum value for optimization")
    max_value: float = Field(..., description="Maximum value for optimization")
    step: float = Field(..., description="Step size for parameter sweep")

class OptimizationConfig(BaseModel):
    """Configuration for hyperparameter optimization"""
    indicators: List[IndicatorRange] = Field(default=[], description="Indicator parameter ranges")
    thresholds: List[ThresholdRange] = Field(default=[], description="Strategy threshold ranges")
    max_combinations: int = Field(default=100, description="Maximum parameter combinations to test")

class OptimizedStrategyMethods(StrategyMethods):
    """Enhanced strategy methods with optimization configuration"""
    optimization_config: OptimizationConfig = Field(default_factory=OptimizationConfig)

class OptimizationResult(BaseModel):
    """Results from hyperparameter optimization"""
    best_parameters: Dict[str, Any] = Field(..., description="Best parameter combination found")
    best_return: float = Field(..., description="Best total return achieved")
    best_sharpe: float = Field(..., description="Best Sharpe ratio achieved")
    best_composite_score: float = Field(..., description="Best composite score achieved")
    best_max_drawdown: float = Field(..., description="Max drawdown of best strategy")
    best_total_trades: int = Field(..., description="Number of trades in best strategy")
    in_sample_return: float = Field(..., description="In-sample return of best strategy")
    out_of_sample_return: float = Field(..., description="Out-of-sample return of best strategy")
    total_combinations_tested: int = Field(..., description="Number of parameter combinations tested")
    valid_combinations: int = Field(..., description="Number of combinations passing risk constraints")
    optimization_summary: str = Field(..., description="Summary of optimization results")

class SupervisorEvaluation(BaseModel):
    """Supervisor evaluation of the backtest analysis report"""
    report_readability_score: int = Field(..., ge=0, le=100, description="Score for report readability and coherence (0-100)")
    code_strategy_alignment_score: int = Field(..., ge=0, le=100, description="Score for how well the generated code follows the business question strategy (0-100)")
    optimization_ranges_realism_score: int = Field(..., ge=0, le=100, description="Score for whether optimization parameter ranges are realistic (0-100)")
    analysis_quality_score: int = Field(..., ge=0, le=100, description="Score for analysis quality and proposed business question relevance (0-100)")
    overall_score: int = Field(..., ge=0, le=100, description="Overall weighted average score (0-100)")
    detailed_feedback: str = Field(..., description="Detailed feedback explaining the scores and recommendations")
    key_strengths: List[str] = Field(..., description="List of key strengths identified in the analysis")
    improvement_areas: List[str] = Field(..., description="List of areas that need improvement")

class BacktestRequest(BaseModel):
    """Request model for backtest strategy"""
    data_source: str
    ticker: str
    business_question: str

class BacktestResponse(BaseModel):
    """Response model for backtest strategy"""
    task_id: str
    message: str

class TaskStatus(BaseModel):
    """Task status model"""
    status: str
    progress: Optional[int] = None
    message: Optional[str] = None
    error: Optional[str] = None
    code: Optional[str] = None
    analysis: Optional[str] = None
    chart_path: Optional[str] = None
    strategy_methods: Optional[StrategyMethods] = None
    optimization_result: Optional[OptimizationResult] = None
    supervisor_evaluation: Optional[SupervisorEvaluation] = None

@router.get("/info")
async def get_backtest_info() -> Dict[str, Any]:
    """
    Get basic information about the backtest strategy functionality.

    Returns:
        Dict[str, Any]: Basic information about the backtest strategy
    """
    try:
        return {
            "status": "success",
            "message": "Backtest Strategy API is operational",
            "version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"Error getting backtest info: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get backtest info: {str(e)}"
        )



@router.get("/static/{filename}")
async def get_static_file(filename: str):
    """
    Serve static files from the static directory.

    Args:
        filename: The name of the file to serve

    Returns:
        FileResponse: The file response
    """
    try:
        # Create the static directory if it doesn't exist
        static_dir = "software/frontend/static"
        os.makedirs(static_dir, exist_ok=True)

        # Check if the file exists
        file_path = os.path.join(static_dir, filename)
        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=404,
                detail=f"File {filename} not found"
            )

        # Return the file
        return FileResponse(file_path)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error serving static file: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to serve static file: {str(e)}"
        )

@router.post("/run", response_model=BacktestResponse)
async def run_backtest(request: BacktestRequest, background_tasks: BackgroundTasks) -> BacktestResponse:
    """
    Run a backtest strategy with the given parameters.

    Args:
        request: The backtest request parameters
        background_tasks: FastAPI background tasks

    Returns:
        BacktestResponse: Response with task ID
    """
    try:
        logger.info(f"Received backtest request for ticker {request.ticker} with data source {request.data_source}")

        # Generate a unique task ID
        task_id = str(uuid.uuid4())

        # Initialize task status
        tasks[task_id] = {
            "status": "pending",
            "progress": 0,
            "message": "Task queued"
        }

        # Add the backtest task to background tasks
        background_tasks.add_task(generate_backtest_code, task_id, request)

        return BacktestResponse(
            task_id=task_id,
            message="Backtest task queued successfully"
        )
    except Exception as e:
        logger.error(f"Error running backtest: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to run backtest: {str(e)}"
        )

@router.get("/status/{task_id}", response_model=TaskStatus)
async def get_task_status(task_id: str) -> TaskStatus:
    """
    Get the status of a backtest task.

    Args:
        task_id: The ID of the task to check

    Returns:
        TaskStatus: The current status of the task
    """
    try:
        if task_id not in tasks:
            raise HTTPException(
                status_code=404,
                detail=f"Task with ID {task_id} not found"
            )

        return TaskStatus(**tasks[task_id])
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting task status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get task status: {str(e)}"
        )

async def analyze_backtest_results(business_question: str, ticker: str, optimization_result: OptimizationResult, strategy_methods: OptimizedStrategyMethods, original_performance: dict = None, chart_path: str = None, data: pd.DataFrame = None, unique_id: str = None) -> str:
    """
    Analyze backtest results and return concise markdown report.
    Uses LLM analysis for strategies that generated signals, regardless of returns.
    """
    try:
        # Check if strategy generated any signals (trades)
        has_signals = (original_performance.get('total_trades', 0) > 0 if original_performance else False)

        # If we have optimization results, also check optimized performance
        if optimization_result and optimization_result.valid_combinations > 0:
            optimized_metrics = await calculate_optimized_performance(strategy_methods, optimization_result, data, ticker, unique_id)
            has_signals = has_signals or optimized_metrics.get('total_trades', 0) > 0
        else:
            optimized_metrics = {}

        if has_signals and data is not None and unique_id is not None:
            # Strategy generated signals - provide comprehensive analysis regardless of returns
            chart_analysis = ""
            if chart_path:
                chart_analysis = await analyze_chart_image(chart_path, business_question, ticker, original_performance, optimized_metrics)

            # Format comprehensive metrics report
            return format_comprehensive_report(
                business_question,
                ticker,
                original_performance or {},
                optimized_metrics,
                optimization_result,
                chart_analysis
            )
        else:
            # No signals generated - this is the actual failure case
            total_trades = original_performance.get('total_trades', 0) if original_performance else 0
            total_return = original_performance.get('total_return_pct', 0) if original_performance else 0

            # Get parameter ranges that were tested
            indicator_ranges = []
            threshold_ranges = []
            if strategy_methods.optimization_config:
                indicator_ranges = [f"**{ind.name}**: {ind.min_value}-{ind.max_value} (step: {ind.step})" for ind in strategy_methods.optimization_config.indicators]
                threshold_ranges = [f"**{thresh.name}**: {thresh.min_value}-{thresh.max_value} (step: {thresh.step})" for thresh in strategy_methods.optimization_config.thresholds]

            # Generate dynamic analysis using LLM
            llm_connect = get_llm_connect()
            model = await llm_connect.get_llm()

            system_prompt = """You are a quantitative trading analyst. Analyze why a trading strategy generated no signals and provide actionable recommendations with a specific proposed business question.

Focus on:
1. Technical indicator thresholds that may be too restrictive
2. Market conditions that could prevent signal generation
3. Specific parameter adjustments to consider
4. Alternative approaches or indicators

Structure your response as:
**Analysis:** Brief explanation of why no signals were generated (2-3 sentences)

**Proposed Business Question:** A specific, ready-to-test alternative strategy that addresses the issues. Use the same ticker and format as the original question but with adjusted parameters/conditions that should generate signals.

Keep the analysis concise and actionable."""

            human_prompt = f"""Business Question: {business_question}
Ticker: {ticker}
Total Trades Generated: {total_trades}
Strategy Return: {total_return:.2f}%
Optimization Combinations Tested: {optimization_result.total_combinations_tested if optimization_result else 0}

The strategy generated no trading signals. Analyze why this happened and provide specific recommendations for improvement."""

            try:
                response = await model.ainvoke([
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=human_prompt)
                ])
                analysis = response.content
            except Exception:
                analysis = f"The strategy conditions may be too restrictive for {ticker}'s price action during this period."

            # Build parameter ranges section
            parameter_ranges_section = ""
            if indicator_ranges or threshold_ranges:
                parameter_ranges_section = f"""
## Parameter Ranges Tested
{chr(10).join(indicator_ranges + threshold_ranges)}
"""

            return f"""
# Backtest Strategy Analysis

## Strategy Overview
**Business Question:** {business_question}

## Results
⚠️ **No Signals Generated** - The strategy did not produce any trading signals across {optimization_result.total_combinations_tested if optimization_result else 'the tested'} parameter combinations.
{parameter_ranges_section}
## Analysis
{analysis}

## Recommendations
Consider adjusting the strategy parameters or conditions to generate trading opportunities while maintaining the core strategy logic.
"""

    except Exception as e:
        return f"""
# Backtest Strategy Analysis

## Strategy Overview
**Business Question:** {business_question}

## Error
❌ Analysis failed: {str(e)}

## Results
- **Return:** {optimization_result.best_return:.2f}%
- **Combinations Tested:** {optimization_result.total_combinations_tested}
"""


async def generate_raw_strategy_code(business_question: str, ticker: str, data_source: str) -> str:
    """
    Generate complete strategy code using fake conversation technique for consistent results.

    Args:
        business_question: The business question to generate strategy for
        ticker: The ticker symbol
        data_source: The data source being used

    Returns:
        str: Complete strategy code including indicators, entry and exit functions
    """
    try:
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()

        # Dynamic system message based on Anthropic best practices
        system_message = SystemMessage(content=f"""<role>
You are a quantitative trading strategy expert specializing in algorithmic trading code generation with 10+ years of experience in Python-based trading systems.
</role>

<task>
Generate complete Python strategy code for the business question: "{business_question}"
</task>

<code_requirements>
Generate exactly these components in this order:
1. def calculate_indicators(self): - Calculate all technical indicators needed
2. def should_long(self): - Entry conditions for long positions
3. def should_exit_long(self): - Exit conditions for long positions
4. def should_short(self): - Entry conditions for short positions (return False if not needed)
5. def should_exit_short(self): - Exit conditions for short positions (return False if not needed)
6. indicators_for_chart = [...] - List of indicator names for visualization
7. optimization_config = {{...}} - Parameter ranges for optimization
</code_requirements>

<technical_specifications>
- Use pandas_ta syntax: ta.rsi(), ta.sma(), ta.macd(), ta.bbands()
- Access current values: self.df['indicator'].iloc[-1]
- Include validation: len(self.df) >= required_periods and pd.isna() checks
- Return boolean values from should_* methods
- Write clean, production-ready code without comments or hardcoded values
- Generate realistic optimization parameter ranges
</technical_specifications>

<output_format>
Provide only the raw Python code without markdown formatting or explanations.
</output_format>""")

        # Fake human message example
        fake_human_message = HumanMessage(content="Backtest a strategy that buys Meta (META) when the RSI is below 30 and sells when it's above 70 over the past year.")

        # Fake AI response with proper code structure
        fake_ai_response = AIMessage(content="""def calculate_indicators(self):
    if len(self.df) < 50:
        return
    self.df['rsi'] = ta.rsi(self.df['Close'], length=14)

def should_long(self):
    if len(self.df) < 50 or pd.isna(self.df['rsi'].iloc[-1]):
        return False
    return self.df['rsi'].iloc[-1] < 30

def should_exit_long(self):
    if len(self.df) < 50 or pd.isna(self.df['rsi'].iloc[-1]):
        return False
    return self.df['rsi'].iloc[-1] > 70

def should_short(self):
    return False

def should_exit_short(self):
    return False

indicators_for_chart = ['rsi']

optimization_config = {
    'indicators': [
        {'name': 'rsi_period', 'base_value': 14, 'min_value': 10, 'max_value': 20, 'step': 2}
    ],
    'thresholds': [
        {'name': 'oversold_threshold', 'base_value': 30, 'min_value': 20, 'max_value': 40, 'step': 5},
        {'name': 'overbought_threshold', 'base_value': 70, 'min_value': 60, 'max_value': 80, 'step': 5}
    ]
}""")

        # Real human message
        real_human_message = HumanMessage(content=f"Backtest strategy: {business_question}")

        # Use fake conversation to prime the model
        messages = [
            system_message,
            fake_human_message,
            fake_ai_response,
            real_human_message
        ]

        response = await model.ainvoke(messages)

        # Debug the raw response
        from software.ai.graph.director_state import print_debug
        print_debug(f"🎯 RAW STRATEGY CODE GENERATED", "CodeGen")
        print_debug(f"📝 Response length: {len(response.content)} characters", "CodeGen")
        print_debug(f"🔍 Full generated code:\n{response.content}", "CodeGen")

        return response.content

    except Exception as e:
        logger.error(f"Error generating raw strategy code: {str(e)}")
        return ""

async def parse_strategy_code(raw_code: str) -> OptimizedStrategyMethods:
    """
    Parse raw strategy code into structured OptimizedStrategyMethods.

    Args:
        raw_code: Raw strategy code string

    Returns:
        OptimizedStrategyMethods: Parsed and structured strategy methods
    """
    try:
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(OptimizedStrategyMethods)

        # Dynamic parsing system message based on Anthropic best practices with model schema
        system_message = SystemMessage(content=f"""<role>
You are a Python code parser specializing in extracting trading strategy components from raw code with expertise in financial algorithm parsing.
</role>

<task>
Parse the provided raw Python strategy code and extract specific components into the exact structured format defined by the schema below.
</task>

<target_schema>
{OptimizedStrategyMethods.model_json_schema()}
</target_schema>

<extraction_requirements>
Extract these exact components following the schema above:
1. calculate_indicators_code: Method body only (without def signature)
2. should_long_code: Method body only (without def signature)
3. should_exit_long_code: Method body only (without def signature)
4. should_short_code: Method body only (without def signature, or "return False" if not present)
5. should_exit_short_code: Method body only (without def signature, or "return False" if not present)
6. indicators_for_chart: Python list of indicator column names as strings
7. optimization_config: Must follow the OptimizationConfig schema with indicators and thresholds arrays
</extraction_requirements>

<parsing_rules>
- Extract only method bodies, never include function signatures
- Preserve exact indentation and code structure
- Include all validation logic and return statements
- For optimization_config: Convert flat parameter dictionaries to proper IndicatorRange/ThresholdRange objects
- If a method is missing, provide appropriate default (e.g., "return False" for optional methods)
- Follow the exact schema structure provided above
</parsing_rules>

<quality_standards>
- Ensure extracted code is syntactically valid Python
- Maintain all technical indicator calculations exactly as written
- Preserve all conditional logic and validation checks
- Extract optimization parameters with correct data types matching the schema
- Ensure optimization_config follows the nested structure with proper IndicatorRange and ThresholdRange objects
</quality_standards>""")

        human_message = HumanMessage(content=f"Parse this strategy code:\n\n{raw_code}")

        response = await structured_model.ainvoke([system_message, human_message])

        # Debug the parsed response
        from software.ai.graph.director_state import print_debug
        print_debug(f"🎯 STRATEGY CODE PARSED", "CodeParse")
        print_debug(f"📊 Indicators: {len(response.optimization_config.indicators)}, Thresholds: {len(response.optimization_config.thresholds)}", "CodeParse")
        print_debug(f"🔍 Calculate indicators length: {len(response.calculate_indicators_code)} chars", "CodeParse")
        print_debug(f"🔍 Should long length: {len(response.should_long_code)} chars", "CodeParse")
        print_debug(f"📈 Indicators for chart: {response.indicators_for_chart}", "CodeParse")

        logger.info(f"Parsed strategy methods - indicators: {len(response.optimization_config.indicators)}, thresholds: {len(response.optimization_config.thresholds)}")
        return response

    except Exception as e:
        logger.error(f"Error parsing strategy code: {str(e)}")
        # Return default structure on error
        return OptimizedStrategyMethods(
            calculate_indicators_code="return",
            should_long_code="return False",
            should_exit_long_code="return False",
            indicators_for_chart=[],
            optimization_config=OptimizationConfig()
        )

async def extract_ticker_from_question(business_question: str) -> str:
    """
    Extract ticker symbol from business question using LLM.

    Args:
        business_question: The business question to extract ticker from

    Returns:
        str: The extracted ticker symbol
    """
    try:
        # First try simple regex extraction
        ticker_match = re.search(r'\(([A-Z]+)\)', business_question)
        if ticker_match:
            return ticker_match.group(1)

        # If regex fails, use LLM
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        structured_model = model.with_structured_output(TickerSelection)

        system_message = SystemMessage(content="""
<role>
You are a financial data parser specializing in extracting stock ticker symbols from text.
</role>

<task>
Extract the stock ticker symbol from the business question.
</task>

<extraction_rules>
1. Look for stock symbols in parentheses (e.g., "Apple (AAPL)")
2. Return only the ticker symbol without parentheses
3. If multiple tickers exist, select the most relevant one to the main strategy
4. If no ticker is explicitly mentioned, infer the most likely ticker from context
</extraction_rules>
""")

        human_message = HumanMessage(content=f"""
<business_question>
{business_question}
</business_question>

Extract the ticker symbol from this business question.
""")

        response = await structured_model.ainvoke([system_message, human_message])
        return response.ticker
    except Exception as e:
        logger.error(f"Error extracting ticker: {str(e)}")
        # Fallback to a default ticker
        return "AAPL"

async def evaluate_with_supervisor(business_question: str, ticker: str, final_report: str, strategy_methods: OptimizedStrategyMethods, optimization_result: OptimizationResult) -> tuple[SupervisorEvaluation, str]:
    """
    Supervisor evaluation of the backtest analysis report with 4 key scores.

    Args:
        business_question: Original business question
        ticker: Stock ticker symbol
        final_report: The complete analysis report
        strategy_methods: Generated strategy methods
        optimization_result: Optimization results

    Returns:
        tuple[SupervisorEvaluation, str]: Structured evaluation with scores and feedback, and the LLM ID used
    """
    try:
        from software.ai.graph.director_state import print_debug

        print_debug(f"🎯 SUPERVISOR EVALUATION STARTED", "Supervisor")
        print_debug(f"📊 Business Question: {business_question[:100]}...", "Supervisor")
        print_debug(f"📈 Ticker: {ticker}", "Supervisor")
        print_debug(f"📝 Report Length: {len(final_report)} characters", "Supervisor")
        print_debug(f"⚙️  Strategy Methods: {len(strategy_methods.optimization_config.indicators)} indicators, {len(strategy_methods.optimization_config.thresholds)} thresholds", "Supervisor")

        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm_by_id("67f972e51c64621068267398")

        # Get the default LLM configuration to capture the LLM ID
        from software.db.llm_repository import LLMRepository
        llm_repo = LLMRepository()
        default_llm_config = await llm_repo.get_default_llm("Completion", debug=False)
        llm_id_used = str(default_llm_config.get("_id")) if default_llm_config else "unknown"

        print_debug(f"🤖 Using LLM ID: {llm_id_used}", "Supervisor")

        structured_model = model.with_structured_output(SupervisorEvaluation)

        system_prompt = """You are a Senior Quantitative Trading Supervisor evaluating backtest analysis reports. Your role is to assess the quality and accuracy of trading strategy analysis with 4 specific scores.

<evaluation_criteria>
1. Report Readability (0-100): Is the report well-structured, clear, and easy to understand? Are metrics properly formatted and conclusions logical?

2. Code-Strategy Alignment (0-100): Does the generated code actually implement what the business question requested? Are entry/exit conditions correctly coded?

3. Optimization Ranges Realism (0-100): Are the parameter ranges for optimization realistic and appropriate for the strategy type? Not too tight or too wide?

4. Analysis Quality (0-100): If no signals were generated, does the analysis correctly identify why and provide a relevant proposed business question that addresses the optimization attempts?
</evaluation_criteria>

<scoring_guidelines>
- 90-100: Excellent, institutional quality
- 80-89: Good, minor improvements needed
- 70-79: Acceptable, some issues present
- 60-69: Below standard, significant improvements needed
- 0-59: Poor, major issues requiring rework
</scoring_guidelines>

<evaluation_focus>
- Check if code logic matches business question requirements exactly
- Verify optimization parameter ranges are market-appropriate
- Assess if analysis explains failures and provides actionable alternatives
- Evaluate overall report coherence and professional presentation
</evaluation_focus>"""

        human_prompt = f"""<business_question>
{business_question}
</business_question>

<ticker>
{ticker}
</ticker>

<final_report>
{final_report}
</final_report>

<strategy_code_summary>
Indicators: {[ind.name for ind in strategy_methods.optimization_config.indicators]}
Indicator Ranges: {[(ind.name, ind.min_value, ind.max_value, ind.step) for ind in strategy_methods.optimization_config.indicators]}
Thresholds: {[thresh.name for thresh in strategy_methods.optimization_config.thresholds]}
Threshold Ranges: {[(thresh.name, thresh.min_value, thresh.max_value, thresh.step) for thresh in strategy_methods.optimization_config.thresholds]}
</strategy_code_summary>

<optimization_results>
Total Combinations Tested: {optimization_result.total_combinations_tested if optimization_result else 0}
Valid Combinations: {optimization_result.valid_combinations if optimization_result else 0}
Best Return: {optimization_result.best_return if optimization_result else 0}%
Best Sharpe: {optimization_result.best_sharpe if optimization_result else 0}
</optimization_results>

Evaluate this backtest analysis report and provide scores for the 4 criteria."""

        print_debug(f"🤖 Sending evaluation request to LLM...", "Supervisor")

        response = await structured_model.ainvoke([
            SystemMessage(content=system_prompt),
            HumanMessage(content=human_prompt)
        ])

        print_debug(f"✅ SUPERVISOR EVALUATION COMPLETED", "Supervisor")
        print_debug(f"📊 Overall Score: {response.overall_score}/100", "Supervisor")
        print_debug(f"📈 Readability: {response.report_readability_score}/100", "Supervisor")
        print_debug(f"🎯 Code Alignment: {response.code_strategy_alignment_score}/100", "Supervisor")
        print_debug(f"⚙️  Optimization Ranges: {response.optimization_ranges_realism_score}/100", "Supervisor")
        print_debug(f"🔍 Analysis Quality: {response.analysis_quality_score}/100", "Supervisor")
        print_debug(f"💪 Strengths: {len(response.key_strengths)} items", "Supervisor")
        print_debug(f"🔧 Improvements: {len(response.improvement_areas)} items", "Supervisor")

        return response, llm_id_used

    except Exception as e:
        from software.ai.graph.director_state import print_debug
        logger.error(f"Error in supervisor evaluation: {str(e)}")
        print_debug(f"❌ SUPERVISOR EVALUATION FAILED: {str(e)}", "Supervisor")
        print_debug(f"🔧 Returning default evaluation scores", "Supervisor")

        # Return default evaluation on error
        return SupervisorEvaluation(
            report_readability_score=50,
            code_strategy_alignment_score=50,
            optimization_ranges_realism_score=50,
            analysis_quality_score=50,
            overall_score=50,
            detailed_feedback=f"Supervisor evaluation failed: {str(e)}",
            key_strengths=["Analysis completed"],
            improvement_areas=["Supervisor evaluation system needs debugging"]
        ), "unknown"

async def generate_backtest_code(task_id: str, request: BacktestRequest):
    """
    Generate Python code for a backtest strategy and execute it.

    Args:
        task_id: The ID of the task
        request: The backtest request parameters
    """
    try:
        # Update task status to in progress
        tasks[task_id] = {
            "status": "in_progress",
            "progress": 10,
            "message": "Starting backtest code generation"
        }

        # Extract ticker if not provided
        ticker = request.ticker
        if not ticker:
            tasks[task_id] = {
                "status": "in_progress",
                "progress": 20,
                "message": "Extracting ticker from business question"
            }
            ticker = await extract_ticker_from_question(request.business_question)

        # Update task with ticker info
        tasks[task_id] = {
            "status": "in_progress",
            "progress": 30,
            "message": f"Generating code for {ticker} using {request.data_source}"
        }

        # Generate unique ID for the chart filename
        unique_id = str(uuid.uuid4())[:8]

        # Update progress for LLM strategy generation
        tasks[task_id]["progress"] = 40
        tasks[task_id]["message"] = "Generating raw strategy code with AI"

        # Step 1: Generate complete strategy code using fake conversation technique
        raw_strategy_code = await generate_raw_strategy_code(
            request.business_question, ticker, request.data_source
        )

        # Log the raw code for debugging
        logger.info(f"Generated raw strategy code: {raw_strategy_code[:500]}...")

        if not raw_strategy_code:
            raise ValueError("Failed to generate strategy code")

        # Update progress for parsing
        tasks[task_id]["progress"] = 45
        tasks[task_id]["message"] = "Parsing strategy code into structured format"

        # Step 2: Parse the raw code into structured format
        strategy_methods = await parse_strategy_code(raw_strategy_code)

        # Update task status
        tasks[task_id] = {
            "status": "in_progress",
            "progress": 60,
            "message": "Strategy methods generated and parsed, loading data for execution",
            "code": f"Strategy methods generated successfully"
        }

        # Load data for the ticker using the specified data source
        try:
            # Get the loader class
            loader_class = registry.get_loader_class(request.data_source)
            if not loader_class:
                raise ValueError(f"Data loader {request.data_source} not found")

            # Update task status
            tasks[task_id] = {
                "status": "in_progress",
                "progress": 70,
                "message": f"Loading data for {ticker} using {request.data_source}"
            }

            # Load the data
            app = StockAnalysisApp(ticker=ticker)
            app.load_data(loader_class=loader_class)

            if app.data is None or app.data.empty:
                raise ValueError(f"No data available for ticker {ticker} with data source {request.data_source}")

            # Update task status
            tasks[task_id] = {
                "status": "in_progress",
                "progress": 80,
                "message": "Data loaded, creating strategy template"
            }

            # Create strategy using template with AI-generated methods
            result_path, optimization_result, original_performance = await create_strategy_with_template_and_optimization(
                strategy_methods, app.data, ticker, unique_id, task_id
            )

            if result_path:
                # Update progress for analysis generation
                tasks[task_id]["progress"] = 95
                tasks[task_id]["message"] = "Generating analysis report"

                # Generate markdown analysis using backtest results
                markdown_analysis = await analyze_backtest_results(
                    request.business_question,
                    ticker,
                    optimization_result,
                    strategy_methods,
                    original_performance,
                    result_path,
                    app.data,
                    unique_id
                )

                # Update progress for supervisor evaluation
                tasks[task_id]["progress"] = 98
                tasks[task_id]["message"] = "Running supervisor evaluation"

                # Run supervisor evaluation on the final report
                supervisor_evaluation, llm_id_used = await evaluate_with_supervisor(
                    request.business_question,
                    ticker,
                    markdown_analysis,
                    strategy_methods,
                    optimization_result
                )

                # Save evaluation data to repository
                try:
                    eval_repo = await BacktestStrategyEvalRepository.create()
                    await eval_repo.save_evaluation(
                        request.business_question,
                        ticker,
                        {
                            'report_readability_score': supervisor_evaluation.report_readability_score,
                            'code_strategy_alignment_score': supervisor_evaluation.code_strategy_alignment_score,
                            'optimization_ranges_realism_score': supervisor_evaluation.optimization_ranges_realism_score,
                            'analysis_quality_score': supervisor_evaluation.analysis_quality_score,
                            'overall_score': supervisor_evaluation.overall_score,
                            'detailed_feedback': supervisor_evaluation.detailed_feedback,
                            'key_strengths': supervisor_evaluation.key_strengths,
                            'improvement_areas': supervisor_evaluation.improvement_areas
                        },
                        llm_used=llm_id_used
                    )
                    logger.info(f"Evaluation data saved for {ticker}")
                except Exception as e:
                    logger.error(f"Failed to save evaluation data: {str(e)}")
                    # Don't fail the entire process if evaluation saving fails

                # Update task status to completed
                tasks[task_id] = {
                    "status": "completed",
                    "progress": 100,
                    "message": "Backtest with optimization completed successfully",
                    "code": f"Strategy methods generated successfully",
                    "chart_path": f"/api/backtest-strategy/static/backtest_strategy_{ticker.lower()}_{unique_id}.png",
                    "analysis": markdown_analysis,
                    "strategy_methods": strategy_methods,
                    "optimization_result": optimization_result,
                    "supervisor_evaluation": supervisor_evaluation
                }
            else:
                # Execution failed
                tasks[task_id] = {
                    "status": "completed",
                    "progress": 100,
                    "message": "Backtest strategy generation completed, but execution failed",
                    "code": f"Strategy methods generated successfully",
                    "error": "Failed to generate chart",
                    "analysis": f"Generated strategy methods for {ticker} based on '{request.business_question}' but execution failed.",
                    "strategy_methods": strategy_methods
                }
        except Exception as e:
            logger.error(f"Error executing backtest: {str(e)}")
            tasks[task_id] = {
                "status": "completed",
                "progress": 100,
                "message": "Backtest strategy generation completed, but execution failed",
                "code": f"Strategy methods generated",
                "error": f"Error executing strategy: {str(e)}",
                "analysis": f"Generated strategy methods for {ticker} based on '{request.business_question}' but execution failed: {str(e)}",
                "strategy_methods": strategy_methods
            }
    except Exception as e:
        logger.error(f"Error generating backtest strategy: {str(e)}")
        tasks[task_id] = {
            "status": "failed",
            "progress": 0,
            "message": "Backtest strategy generation failed",
            "error": str(e)
        }


async def create_strategy_with_template_and_optimization(
    strategy_methods: OptimizedStrategyMethods,
    data: pd.DataFrame,
    ticker: str,
    unique_id: str,
    task_id: str
) -> Tuple[str, Optional[OptimizationResult], Optional[dict]]:
    """
    Create and execute a strategy with hyperparameter optimization.

    Args:
        strategy_methods: AI-generated strategy methods with optimization config
        data: The data to use for backtesting
        ticker: The ticker symbol
        unique_id: A unique identifier for the chart filename
        task_id: Task ID for progress updates

    Returns:
        Tuple[str, OptimizationResult, dict]: The path to the generated chart, optimization results, and original performance
    """

    # First run the original strategy and calculate its performance
    original_chart_path = await create_strategy_with_template(strategy_methods, data, ticker, unique_id)

    # Calculate original strategy performance
    original_performance = await calculate_original_performance(strategy_methods, data, ticker, unique_id)

    # Check if optimization is configured
    if not strategy_methods.optimization_config.indicators and not strategy_methods.optimization_config.thresholds:
        return original_chart_path, None, original_performance

    # Update task status for optimization
    tasks[task_id]["progress"] = 85
    tasks[task_id]["message"] = "Running hyperparameter optimization"

    # Run optimization
    optimization_result = await run_hyperparameter_optimization(
        strategy_methods, data, ticker, unique_id, task_id
    )

    # Generate optimized chart with both original and optimized results
    optimized_chart_path = await create_optimized_chart(
        strategy_methods, data, ticker, unique_id, optimization_result
    )

    return optimized_chart_path or original_chart_path, optimization_result, original_performance

def calculate_comprehensive_metrics(strategy) -> dict:
    """Calculate comprehensive performance metrics inspired by Jesse AI."""
    try:
        if not hasattr(strategy, 'signals') or strategy.signals.empty:
            return {}

        signals = strategy.signals

        # Basic setup
        starting_balance = 10000.0

        # Calculate position changes for trade detection
        position_changes = signals['position'].diff()
        trades = []

        # Extract individual trades
        current_trade = None
        for i, (idx, row) in enumerate(signals.iterrows()):
            if position_changes.iloc[i] > 0:  # Entry
                current_trade = {
                    'entry_idx': i,
                    'entry_price': strategy.df.loc[idx, 'Close'],
                    'entry_date': idx
                }
            elif position_changes.iloc[i] < 0 and current_trade:  # Exit
                exit_price = strategy.df.loc[idx, 'Close']
                pnl = (exit_price - current_trade['entry_price']) / current_trade['entry_price']
                pnl_dollar = pnl * starting_balance

                trades.append({
                    'entry_date': current_trade['entry_date'],
                    'exit_date': idx,
                    'entry_price': current_trade['entry_price'],
                    'exit_price': exit_price,
                    'pnl_pct': pnl * 100,
                    'pnl_dollar': pnl_dollar,
                    'holding_period': (idx - current_trade['entry_date']).days,
                    'is_winning': pnl > 0
                })
                current_trade = None

        # Performance Metrics
        final_balance = signals['strategy_cumulative_returns'].iloc[-1] * starting_balance
        total_pnl = final_balance - starting_balance
        total_return_pct = (final_balance / starting_balance - 1) * 100

        # Win/Loss Analysis
        winning_trades = [t for t in trades if t['is_winning']]
        losing_trades = [t for t in trades if not t['is_winning']]

        win_rate = len(winning_trades) / len(trades) * 100 if trades else 0
        avg_win = np.mean([t['pnl_dollar'] for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t['pnl_dollar'] for t in losing_trades]) if losing_trades else 0
        avg_win_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0

        # Risk Metrics
        returns = signals['strategy_returns'].dropna()
        sharpe_ratio = returns.mean() / returns.std() * (252 ** 0.5) if returns.std() > 0 else 0

        # Sortino ratio (downside deviation)
        downside_returns = returns[returns < 0]
        downside_std = downside_returns.std() if len(downside_returns) > 0 else 0
        sortino_ratio = returns.mean() / downside_std * (252 ** 0.5) if downside_std > 0 else 0

        # Max drawdown
        cumulative = signals['strategy_cumulative_returns']
        peak = cumulative.cummax()
        drawdown = (cumulative - peak) / peak
        max_drawdown = drawdown.min() * 100

        # Trade streaks
        winning_streaks = []
        losing_streaks = []
        current_streak = 0
        current_type = None

        for trade in trades:
            if trade['is_winning']:
                if current_type == 'win':
                    current_streak += 1
                else:
                    if current_type == 'loss' and current_streak > 0:
                        losing_streaks.append(current_streak)
                    current_streak = 1
                    current_type = 'win'
            else:
                if current_type == 'loss':
                    current_streak += 1
                else:
                    if current_type == 'win' and current_streak > 0:
                        winning_streaks.append(current_streak)
                    current_streak = 1
                    current_type = 'loss'

        # Add final streak
        if current_type == 'win' and current_streak > 0:
            winning_streaks.append(current_streak)
        elif current_type == 'loss' and current_streak > 0:
            losing_streaks.append(current_streak)

        # Largest trades
        largest_win = max([t['pnl_dollar'] for t in winning_trades]) if winning_trades else 0
        largest_loss = min([t['pnl_dollar'] for t in losing_trades]) if losing_trades else 0

        # Expectancy
        expectancy = (win_rate / 100 * avg_win) + ((100 - win_rate) / 100 * avg_loss) if trades else 0
        expectancy_pct = expectancy / starting_balance * 100 if starting_balance > 0 else 0

        # Gross profit/loss
        gross_profit = sum([t['pnl_dollar'] for t in winning_trades])
        gross_loss = sum([t['pnl_dollar'] for t in losing_trades])

        # Average holding period
        avg_holding_period = np.mean([t['holding_period'] for t in trades]) if trades else 0

        # Calculate Buy & Hold return
        buy_hold_return_pct = 0.0
        if 'cumulative_returns' in signals.columns and len(signals) > 0:
            buy_hold_return_pct = (signals['cumulative_returns'].iloc[-1] - 1) * 100

        return {
            # Performance Metrics
            'total_pnl': total_pnl,
            'total_return_pct': total_return_pct,
            'win_rate': win_rate,
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'avg_win_loss_ratio': avg_win_loss_ratio,
            'avg_win': avg_win,
            'avg_loss': avg_loss,

            # Risk Metrics
            'max_drawdown': max_drawdown,
            'largest_winning_trade': largest_win,
            'largest_losing_trade': largest_loss,
            'total_winning_streak': max(winning_streaks) if winning_streaks else 0,
            'total_losing_streak': max(losing_streaks) if losing_streaks else 0,
            'expectancy': expectancy,
            'expectancy_pct': expectancy_pct,
            'avg_holding_period': avg_holding_period,
            'gross_profit': gross_profit,
            'gross_loss': gross_loss,

            # Trade Metrics
            'total_trades': len(trades),
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'starting_balance': starting_balance,
            'finishing_balance': final_balance,

            # Buy & Hold Metrics
            'buy_hold_return_pct': buy_hold_return_pct
        }

    except Exception as e:
        return {}

async def calculate_optimized_performance(strategy_methods: OptimizedStrategyMethods, optimization_result: OptimizationResult, data: pd.DataFrame, ticker: str, unique_id: str) -> dict:
    """Calculate comprehensive performance metrics for the optimized strategy."""
    try:
        # Generate replacement patterns and create optimized strategy
        replacement_patterns = await generate_replacement_patterns(strategy_methods)
        optimized_strategy_methods = create_modified_strategy(strategy_methods, optimization_result.best_parameters, replacement_patterns)
        optimized_template = BacktestTemplate(data, optimized_strategy_methods, ticker, f"{unique_id}_opt")
        optimized_strategy = optimized_template._create_strategy_instance()

        # Calculate comprehensive metrics
        return calculate_comprehensive_metrics(optimized_strategy)

    except Exception as e:
        return {}

def calculate_buy_hold_return(original_metrics: dict, optimized_metrics: dict) -> float:
    """Calculate Buy & Hold return dynamically from strategy metrics."""
    try:
        # Try to get buy_hold_return_pct from either metrics dict (new field)
        buy_hold_return = (
            original_metrics.get('buy_hold_return_pct') or
            optimized_metrics.get('buy_hold_return_pct')
        )

        if buy_hold_return is not None:
            return float(buy_hold_return)

        # If no buy & hold data available, return 0
        return 0.0

    except Exception as e:
        return 0.0

async def analyze_chart_image(chart_path: str, business_question: str, ticker: str, original_metrics: dict, optimized_metrics: dict) -> str:
    """Analyze the backtest chart image to provide insights on signal timing and market context."""
    try:
        import base64

        # Read and encode the image
        with open(chart_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')

        # Calculate Buy & Hold return dynamically from the chart data
        buy_hold_return = calculate_buy_hold_return(original_metrics, optimized_metrics)

        # Get LLM for vision analysis
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm_by_id("67f972e51c64621068267398")

        # Determine market performance context for dynamic prompting
        performance_context = "outperformed" if buy_hold_return < max(
            original_metrics.get('total_return_pct', 0),
            optimized_metrics.get('total_return_pct', 0)
        ) else "underperformed"

        strategy_complexity = "simple" if original_metrics.get('total_trades', 0) <= 5 else "active"

        system_prompt = f"""<role>
You are a Senior Quantitative Analyst with 15+ years of experience in algorithmic trading and technical analysis. You specialize in backtesting evaluation, chart pattern recognition, and strategy optimization. Your expertise includes identifying market regime changes, signal quality assessment, and providing actionable trading insights.
</role>

<task>
Analyze the provided backtest chart image to deliver professional-grade insights on strategy performance, signal timing, and market dynamics. Your analysis will be used by portfolio managers and strategy developers to improve trading algorithms.
</task>

<context>
- Strategy type: {strategy_complexity} trading approach
- Market performance: Strategy {performance_context} Buy & Hold
- Audience: Portfolio managers and quantitative researchers
- Purpose: Strategy improvement and optimization decisions
- Time constraint: Concise analysis under 150 words
</context>

<analysis_framework>
1. Visual pattern recognition: Identify key chart patterns and market regimes
2. Signal quality assessment: Evaluate entry/exit timing effectiveness
3. Performance attribution: Analyze what drove strategy performance vs Buy & Hold
4. Risk assessment: Observe drawdown patterns and volatility periods
5. Actionable recommendations: Suggest specific improvements based on visual evidence
</analysis_framework>

<output_format>
Structure your analysis as:
- **Signal Timing & Context**: Market conditions and signal effectiveness
- **Entry/Exit Analysis**: Quality of trade execution points
- **Key Observations**: Notable patterns or behaviors from the chart
- **Actionable Insights**: Specific recommendations for improvement
</output_format>

<success_criteria>
Your analysis must:
- Focus on visual chart evidence rather than just numerical metrics
- Provide specific, actionable insights for strategy improvement
- Maintain professional tone suitable for institutional investors
- Identify both strengths and weaknesses in strategy execution
- Connect visual patterns to trading performance outcomes
</success_criteria>"""

        human_prompt = f"""<strategy_details>
Ticker: {ticker}
Strategy Description: {business_question}
</strategy_details>

<performance_metrics>
- Buy & Hold return: {buy_hold_return:.1f}%
- Original strategy return: {original_metrics.get('total_return_pct', 0):.1f}%
- Optimized strategy return: {optimized_metrics.get('total_return_pct', 0):.1f}%
- Original trades: {original_metrics.get('total_trades', 0)}
- Optimized trades: {optimized_metrics.get('total_trades', 0)}
</performance_metrics>

<instructions>
Analyze the attached backtest chart image using your expertise in technical analysis and strategy evaluation. Focus on visual patterns, signal quality, and market dynamics visible in the chart. Provide insights that will help improve this trading strategy.
</instructions>"""

        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=[
                {"type": "text", "text": human_prompt},
                {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{base64_image}"}}
            ])
        ]

        response = await model.ainvoke(messages)
        return response.content

    except Exception as e:
        return ""

def format_comprehensive_report(business_question: str, ticker: str, original_metrics: dict, optimized_metrics: dict, optimization_result: OptimizationResult, chart_analysis: str = "") -> str:
    """Format comprehensive backtest report inspired by Jesse AI."""

    # Helper function to safely get metric values
    def get_metric(metrics, key, default=0, format_str="{:.2f}"):
        try:
            value = metrics.get(key, default)
            if isinstance(value, (int, float)) and not np.isnan(value):
                return format_str.format(value)
            return format_str.format(default)
        except:
            return format_str.format(default)

    # Check if default strategy is optimal (no valid combinations found but strategy has positive returns)
    is_default_optimal = optimization_result.valid_combinations == 0 and optimization_result.best_return > 0

    if is_default_optimal:
        # Use original metrics for the single report when default is optimal
        metrics_to_use = original_metrics
        strategy_title = "Strategy Performance (Default Parameters Optimal)"

        return f"""
# Backtest Strategy Analysis

## Strategy Overview
**Business Question:** {business_question}

## {strategy_title}
- **PNL:** ${get_metric(metrics_to_use, 'total_pnl')} ({get_metric(metrics_to_use, 'total_return_pct')}%)
- **Win Rate:** {get_metric(metrics_to_use, 'win_rate')}%
- **Sharpe Ratio:** {get_metric(metrics_to_use, 'sharpe_ratio', format_str="{:.4f}")}
- **Sortino Ratio:** {get_metric(metrics_to_use, 'sortino_ratio', format_str="{:.4f}")}
- **Average Win/Loss:** {get_metric(metrics_to_use, 'avg_win_loss_ratio', format_str="{:.4f}")}
- **Average Win:** ${get_metric(metrics_to_use, 'avg_win')}
- **Average Loss:** ${get_metric(metrics_to_use, 'avg_loss')}

## Risk Metrics
- **Max Drawdown:** {get_metric(metrics_to_use, 'max_drawdown')}%
- **Largest Winning Trade:** ${get_metric(metrics_to_use, 'largest_winning_trade')}
- **Largest Losing Trade:** ${get_metric(metrics_to_use, 'largest_losing_trade')}
- **Total Winning Streak:** {get_metric(metrics_to_use, 'total_winning_streak', format_str="{:.0f}")}
- **Total Losing Streak:** {get_metric(metrics_to_use, 'total_losing_streak', format_str="{:.0f}")}
- **Expectancy:** ${get_metric(metrics_to_use, 'expectancy')} ({get_metric(metrics_to_use, 'expectancy_pct')}%)
- **Average Holding Period:** {get_metric(metrics_to_use, 'avg_holding_period', format_str="{:.1f}")} days
- **Gross Profit:** ${get_metric(metrics_to_use, 'gross_profit')}
- **Gross Loss:** ${get_metric(metrics_to_use, 'gross_loss')}

## Trade Metrics
- **Total Trades:** {get_metric(metrics_to_use, 'total_trades', format_str="{:.0f}")}
- **Winning Trades:** {get_metric(metrics_to_use, 'winning_trades', format_str="{:.0f}")}
- **Losing Trades:** {get_metric(metrics_to_use, 'losing_trades', format_str="{:.0f}")}
- **Starting Balance:** ${get_metric(metrics_to_use, 'starting_balance')}
- **Finishing Balance:** ${get_metric(metrics_to_use, 'finishing_balance')}

## Optimization Results
- **Combinations Tested:** {optimization_result.total_combinations_tested}
- **Valid Combinations:** {optimization_result.valid_combinations} (passed risk constraints)
- **Best Parameters:** {optimization_result.best_parameters}
- **Composite Score:** {optimization_result.best_composite_score:.2f}
- **In-Sample Return:** {optimization_result.in_sample_return:.2f}%
- **Out-of-Sample Return:** {optimization_result.out_of_sample_return:.2f}%
- **Risk Constraints Applied:**
  - Max Drawdown: ≤ 50%
  - Minimum Trades: ≥ 1
  - Minimum Sharpe: ≥ -2.0

✅ **Default Strategy is Optimal** - No parameter combinations outperformed the original strategy.

{f"## Chart Analysis{chr(10)}{chart_analysis}" if chart_analysis else ""}
"""
    else:
        # Calculate key metrics for executive summary
        buy_hold_return = float(get_metric(original_metrics, 'buy_hold_return_pct', default=0, format_str="{:.2f}"))
        original_return = float(get_metric(original_metrics, 'total_return_pct', default=0, format_str="{:.2f}"))
        optimized_return = float(get_metric(optimized_metrics, 'total_return_pct', default=0, format_str="{:.2f}"))

        # Determine best performing strategy
        best_return = max(original_return, optimized_return)
        best_strategy = "Optimized" if optimized_return > original_return else "Original"

        # Calculate out-of-sample period (30% of data)
        total_combinations = optimization_result.total_combinations_tested
        in_sample_return = optimization_result.in_sample_return
        out_sample_return = optimization_result.out_of_sample_return

        # Performance vs benchmark
        vs_benchmark = "outperformed" if best_return > buy_hold_return else "underperformed"
        performance_gap = abs(best_return - buy_hold_return)

        # Show both original and optimized when optimization found improvements
        return f"""
# Backtest Strategy Analysis

## Executive Summary
**Strategy Performance:** {best_strategy} strategy achieved {best_return:.2f}% return, {vs_benchmark} Buy & Hold ({buy_hold_return:.2f}%) by {performance_gap:.2f}pp. Optimization tested {total_combinations} parameter combinations with {optimization_result.valid_combinations} passing risk constraints. Out-of-sample validation shows {out_sample_return:.2f}% return vs {in_sample_return:.2f}% in-sample, indicating {"robust performance" if out_sample_return > 0 else "potential overfitting"}.

**Risk Assessment:** {best_strategy} strategy Sharpe ratio of {get_metric(optimized_metrics if best_strategy == "Optimized" else original_metrics, 'sharpe_ratio', format_str="{:.4f}")} with maximum drawdown of {get_metric(optimized_metrics if best_strategy == "Optimized" else original_metrics, 'max_drawdown', format_str="{:.2f}")}%. Strategy generated {get_metric(optimized_metrics if best_strategy == "Optimized" else original_metrics, 'total_trades', format_str="{:.0f}")} trades over the testing period.

## Strategy Overview
**Business Question:** {business_question}

## Performance Metrics

### Buy & Hold
- **PNL:** ${(float(get_metric(original_metrics, 'buy_hold_return_pct', default=0, format_str="{:.2f}")) * float(get_metric(original_metrics, 'starting_balance', default=10000, format_str="{:.0f}")) / 100):.2f} ({get_metric(original_metrics, 'buy_hold_return_pct')}%)

### Original Strategy (As Requested)
- **PNL:** ${get_metric(original_metrics, 'total_pnl')} ({get_metric(original_metrics, 'total_return_pct')}%)
- **Win Rate:** {get_metric(original_metrics, 'win_rate')}%
- **Sharpe Ratio:** {get_metric(original_metrics, 'sharpe_ratio', format_str="{:.4f}")}
- **Sortino Ratio:** {get_metric(original_metrics, 'sortino_ratio', format_str="{:.4f}")}
- **Average Win/Loss:** {get_metric(original_metrics, 'avg_win_loss_ratio', format_str="{:.4f}")}
- **Average Win:** ${get_metric(original_metrics, 'avg_win')}
- **Average Loss:** ${get_metric(original_metrics, 'avg_loss')}

### Optimized Strategy
- **PNL:** ${get_metric(optimized_metrics, 'total_pnl')} ({get_metric(optimized_metrics, 'total_return_pct')}%)
- **Win Rate:** {get_metric(optimized_metrics, 'win_rate')}%
- **Sharpe Ratio:** {get_metric(optimized_metrics, 'sharpe_ratio', format_str="{:.4f}")}
- **Sortino Ratio:** {get_metric(optimized_metrics, 'sortino_ratio', format_str="{:.4f}")}
- **Average Win/Loss:** {get_metric(optimized_metrics, 'avg_win_loss_ratio', format_str="{:.4f}")}
- **Average Win:** ${get_metric(optimized_metrics, 'avg_win')}
- **Average Loss:** ${get_metric(optimized_metrics, 'avg_loss')}

## Risk-Adjusted Performance Summary
- **Returns Comparison:** Buy & Hold {buy_hold_return:.2f}% vs Original {original_return:.2f}% vs Optimized {optimized_return:.2f}%
- **Risk-Adjusted Returns:** Original Sharpe {get_metric(original_metrics, 'sharpe_ratio', format_str="{:.4f}")} vs Optimized Sharpe {get_metric(optimized_metrics, 'sharpe_ratio', format_str="{:.4f}")}
- **Downside Protection:** Original Sortino {get_metric(original_metrics, 'sortino_ratio', format_str="{:.4f}")} vs Optimized Sortino {get_metric(optimized_metrics, 'sortino_ratio', format_str="{:.4f}")}
- **Maximum Risk:** Original Drawdown {get_metric(original_metrics, 'max_drawdown', format_str="{:.2f}")}% vs Optimized Drawdown {get_metric(optimized_metrics, 'max_drawdown', format_str="{:.2f}")}%
- **Trade Efficiency:** Original Win Rate {get_metric(original_metrics, 'win_rate', format_str="{:.2f}")}% vs Optimized Win Rate {get_metric(optimized_metrics, 'win_rate', format_str="{:.2f}")}%

## Risk Metrics

### Original Strategy
- **Largest Winning Trade:** ${get_metric(original_metrics, 'largest_winning_trade')}
- **Largest Losing Trade:** ${get_metric(original_metrics, 'largest_losing_trade')}
- **Total Winning Streak:** {get_metric(original_metrics, 'total_winning_streak', format_str="{:.0f}")}
- **Total Losing Streak:** {get_metric(original_metrics, 'total_losing_streak', format_str="{:.0f}")}
- **Expectancy:** ${get_metric(original_metrics, 'expectancy')} ({get_metric(original_metrics, 'expectancy_pct')}%)
- **Average Holding Period:** {get_metric(original_metrics, 'avg_holding_period', format_str="{:.1f}")} days
- **Gross Profit:** ${get_metric(original_metrics, 'gross_profit')}
- **Gross Loss:** ${get_metric(original_metrics, 'gross_loss')}

### Optimized Strategy
- **Largest Winning Trade:** ${get_metric(optimized_metrics, 'largest_winning_trade')}
- **Largest Losing Trade:** ${get_metric(optimized_metrics, 'largest_losing_trade')}
- **Total Winning Streak:** {get_metric(optimized_metrics, 'total_winning_streak', format_str="{:.0f}")}
- **Total Losing Streak:** {get_metric(optimized_metrics, 'total_losing_streak', format_str="{:.0f}")}
- **Expectancy:** ${get_metric(optimized_metrics, 'expectancy')} ({get_metric(optimized_metrics, 'expectancy_pct')}%)
- **Average Holding Period:** {get_metric(optimized_metrics, 'avg_holding_period', format_str="{:.1f}")} days
- **Gross Profit:** ${get_metric(optimized_metrics, 'gross_profit')}
- **Gross Loss:** ${get_metric(optimized_metrics, 'gross_loss')}

## Trade Metrics

### Original Strategy
- **Total Trades:** {get_metric(original_metrics, 'total_trades', format_str="{:.0f}")}
- **Winning Trades:** {get_metric(original_metrics, 'winning_trades', format_str="{:.0f}")}
- **Losing Trades:** {get_metric(original_metrics, 'losing_trades', format_str="{:.0f}")}
- **Starting Balance:** ${get_metric(original_metrics, 'starting_balance')}
- **Finishing Balance:** ${get_metric(original_metrics, 'finishing_balance')}

### Optimized Strategy
- **Total Trades:** {get_metric(optimized_metrics, 'total_trades', format_str="{:.0f}")}
- **Winning Trades:** {get_metric(optimized_metrics, 'winning_trades', format_str="{:.0f}")}
- **Losing Trades:** {get_metric(optimized_metrics, 'losing_trades', format_str="{:.0f}")}
- **Starting Balance:** ${get_metric(optimized_metrics, 'starting_balance')}
- **Finishing Balance:** ${get_metric(optimized_metrics, 'finishing_balance')}

## Optimization Results
- **Combinations Tested:** {optimization_result.total_combinations_tested}
- **Valid Combinations:** {optimization_result.valid_combinations} (passed risk constraints)
- **Best Parameters:** {optimization_result.best_parameters}
- **Composite Score:** {optimization_result.best_composite_score:.2f}
- **Data Split:** 70% in-sample (training) / 30% out-of-sample (validation)
- **In-Sample Return:** {optimization_result.in_sample_return:.2f}% (optimization period)
- **Out-of-Sample Return:** {optimization_result.out_of_sample_return:.2f}% (validation period)
- **Risk Constraints Applied:**
  - Max Drawdown: ≤ 50%
  - Minimum Trades: ≥ 1
  - Minimum Sharpe: ≥ -2.0

{f"## Chart Analysis{chr(10)}{chart_analysis}" if chart_analysis else ""}
"""

async def calculate_original_performance(strategy_methods: OptimizedStrategyMethods, data: pd.DataFrame, ticker: str, unique_id: str) -> dict:
    """Calculate comprehensive performance metrics for the original strategy."""
    try:
        # Create strategy template with original parameters
        strategy_template = BacktestTemplate(data, strategy_methods, ticker, f"{unique_id}_orig")
        strategy = strategy_template._create_strategy_instance()

        # Calculate comprehensive metrics
        return calculate_comprehensive_metrics(strategy)

    except Exception as e:
        return {}

async def create_strategy_with_template(strategy_methods: StrategyMethods, data: pd.DataFrame, ticker: str, unique_id: str) -> str:
    """
    Create and execute a strategy using the template approach with AI-generated methods.

    Args:
        strategy_methods: AI-generated strategy methods
        data: The data to use for backtesting
        ticker: The ticker symbol
        unique_id: A unique identifier for the chart filename

    Returns:
        str: The path to the generated chart
    """

    # Create directory for images if it doesn't exist
    image_dir = "software/library/images"
    os.makedirs(image_dir, exist_ok=True)

    # Create a static directory for serving images
    static_dir = "software/frontend/static"
    os.makedirs(static_dir, exist_ok=True)

    # Default chart path in case of errors
    default_chart_path = os.path.join(image_dir, f"backtest_strategy_{ticker.lower()}_{unique_id}.png")

    try:
        # Create strategy template with AI-generated methods
        strategy_template = BacktestTemplate(data, strategy_methods, ticker, unique_id)

        # Generate chart
        chart_path = strategy_template.create_chart()

        if chart_path and os.path.exists(chart_path):
            # Copy the file to the static directory for serving
            static_path = os.path.join(static_dir, f"backtest_strategy_{ticker.lower()}_{unique_id}.png")
            import shutil
            shutil.copy2(chart_path, static_path)

            return chart_path
        else:
            return default_chart_path

    except Exception as e:
        return default_chart_path


class BacktestTemplate:
    """Template class for backtesting strategies with AI-generated methods."""

    def __init__(self, data: pd.DataFrame, strategy_methods: StrategyMethods, ticker: str, unique_id: str):
        self.data = data.copy()
        self.strategy_methods = strategy_methods
        self.ticker = ticker
        self.unique_id = unique_id
        self.strategy = None

    def create_chart(self) -> str:
        """Create the backtest chart using the template."""

        try:
            # Create strategy instance with AI-generated methods
            self.strategy = self._create_strategy_instance()

            # Generate the 5-panel chart
            chart_path = self._generate_chart()
            return chart_path

        except Exception as e:
            return self._create_error_chart(str(e))

    def _create_strategy_instance(self):
        """Create a strategy instance with AI-generated methods."""
        # Create a dynamic strategy class with AI methods
        class DynamicStrategy:
            def __init__(self, df, strategy_methods):
                self.df = df.copy()
                self.strategy_methods = strategy_methods
                self.signals = pd.DataFrame(index=df.index)
                self.signals['position'] = 0

                # Execute AI-generated methods
                self._execute_calculate_indicators()
                self._generate_signals()
                self._calculate_returns()

            def _execute_calculate_indicators(self):
                """Execute AI-generated calculate_indicators code."""

                try:
                    # Create namespace for execution
                    namespace = {
                        'self': self,
                        'pd': pd,
                        'np': np,
                        'ta': None  # Will try to import pandas_ta
                    }

                    # Try to import pandas_ta
                    try:
                        import pandas_ta as ta
                        namespace['ta'] = ta
                    except ImportError:
                        pass

                    # Wrap the code in a function to avoid 'return' outside function error
                    wrapped_code = f"""
def calculate_indicators_impl():
{chr(10).join('    ' + line for line in self.strategy_methods.calculate_indicators_code.split(chr(10)))}

calculate_indicators_impl()
"""

                    # Execute the AI-generated code
                    exec(wrapped_code, namespace)

                except Exception as e:
                    pass



            def _should_long(self):
                """Execute AI-generated should_long code."""

                try:
                    namespace = {
                        'self': self,
                        'pd': pd,
                        'np': np
                    }

                    # Modify the AI-generated code to use current index instead of negative indices
                    modified_code = self.strategy_methods.should_long_code
                    if hasattr(self, 'current_idx'):
                        # Replace all instances of .iloc[-N] with .iloc[current_idx - (N-1)]
                        import re
                        def replace_iloc(match):
                            offset = int(match.group(1))
                            new_index = self.current_idx - (offset - 1)
                            return f'.iloc[{new_index}]'

                        modified_code = re.sub(r'\.iloc\[-(\d+)\]', replace_iloc, modified_code)

                    # Wrap the code in a function to handle returns properly
                    wrapped_code = f"""
def should_long_impl():
{chr(10).join('    ' + line for line in modified_code.split(chr(10)))}

result = should_long_impl()
"""
                    # Execute the AI-generated code and return result
                    exec(wrapped_code, namespace)
                    result = namespace.get('result', False)
                    return result

                except Exception as e:
                    return False

            def _should_exit_long(self):
                """Execute AI-generated should_exit_long code."""

                try:
                    namespace = {
                        'self': self,
                        'pd': pd,
                        'np': np
                    }

                    # Modify the AI-generated code to use current index instead of negative indices
                    modified_code = self.strategy_methods.should_exit_long_code
                    if hasattr(self, 'current_idx'):
                        # Replace all instances of .iloc[-N] with .iloc[current_idx - (N-1)]
                        import re
                        def replace_iloc(match):
                            offset = int(match.group(1))
                            new_index = self.current_idx - (offset - 1)
                            return f'.iloc[{new_index}]'

                        modified_code = re.sub(r'\.iloc\[-(\d+)\]', replace_iloc, modified_code)

                    # Wrap the code in a function to handle returns properly
                    wrapped_code = f"""
def should_exit_long_impl():
{chr(10).join('    ' + line for line in modified_code.split(chr(10)))}

result = should_exit_long_impl()
"""
                    # Execute the AI-generated code and return result
                    exec(wrapped_code, namespace)
                    result = namespace.get('result', False)
                    return result

                except Exception as e:
                    return False

            def _generate_signals(self):
                """Generate trading signals using AI methods."""

                try:
                    # Initialize position column
                    self.signals['position'] = 0

                    # Ensure we have enough data
                    if len(self.df) < 50:
                        return

                    # Initialize position tracking variables
                    self.current_position_days = 0

                    # Loop through data to generate signals
                    for i in range(1, len(self.df)):
                        # Update current index for AI methods
                        self.current_idx = i

                        # Default to previous position (hold)
                        self.signals.loc[self.signals.index[i], 'position'] = self.signals.loc[self.signals.index[i-1], 'position']

                        # Update position days counter
                        if self.signals.loc[self.signals.index[i-1], 'position'] == 1:
                            self.current_position_days += 1
                        else:
                            self.current_position_days = 0

                        # Check for long entry
                        should_long_result = self._should_long()

                        if self.signals.loc[self.signals.index[i-1], 'position'] == 0 and should_long_result:
                            self.signals.loc[self.signals.index[i], 'position'] = 1
                            self.current_position_days = 1  # Reset counter on new position

                        # Check for long exit
                        elif self.signals.loc[self.signals.index[i-1], 'position'] == 1:
                            should_exit_result = self._should_exit_long()
                            if should_exit_result:
                                self.signals.loc[self.signals.index[i], 'position'] = 0
                                self.current_position_days = 0  # Reset counter on exit

                except Exception as e:
                    self.signals['position'] = 0

            def _calculate_returns(self):
                """Calculate strategy returns and performance metrics."""
                try:
                    # Calculate daily returns
                    self.signals['returns'] = self.df['Close'].pct_change()

                    # Calculate strategy returns
                    self.signals['strategy_returns'] = self.signals['position'].shift(1) * self.signals['returns']

                    # Calculate cumulative returns
                    self.signals['cumulative_returns'] = (1 + self.signals['returns']).cumprod()
                    self.signals['strategy_cumulative_returns'] = (1 + self.signals['strategy_returns']).cumprod()

                    # Fill NaN values only for returns columns, not cumulative returns
                    self.signals['returns'] = self.signals['returns'].fillna(0)
                    self.signals['strategy_returns'] = self.signals['strategy_returns'].fillna(0)

                    # Ensure cumulative returns start from 1.0
                    self.signals['cumulative_returns'] = self.signals['cumulative_returns'].fillna(1.0)
                    self.signals['strategy_cumulative_returns'] = self.signals['strategy_cumulative_returns'].fillna(1.0)

                except Exception as e:
                    print(f"Error calculating returns: {str(e)}")
                    # Create empty returns columns
                    self.signals['returns'] = 0.0
                    self.signals['strategy_returns'] = 0.0
                    self.signals['cumulative_returns'] = 1.0
                    self.signals['strategy_cumulative_returns'] = 1.0

        return DynamicStrategy(self.data, self.strategy_methods)

    def _generate_chart(self) -> str:
        """Generate the 5-panel backtest chart."""
        try:
            import matplotlib.pyplot as plt
            from matplotlib.gridspec import GridSpec

            # Create figure and subplots
            fig = plt.figure(figsize=(14, 12))
            gs = GridSpec(5, 1, height_ratios=[2, 1, 1, 1, 1], figure=fig)

            # Panel 1: Price chart with signals
            ax1 = fig.add_subplot(gs[0])
            ax1.plot(self.data.index, self.data['Close'], label='Price', color='black', linewidth=1)

            # Panel 1 is only for price and signals - no indicators

            # Add buy/sell signals
            if 'position' in self.strategy.signals.columns:
                buy_signals = self.strategy.signals['position'].diff() > 0
                sell_signals = self.strategy.signals['position'].diff() < 0

                if buy_signals.any():
                    buy_dates = self.strategy.signals.index[buy_signals]
                    buy_prices = self.data.loc[buy_dates, 'Close']
                    ax1.scatter(buy_dates, buy_prices, marker='^', color='green', s=100, label='Buy Signal', zorder=5)

                if sell_signals.any():
                    sell_dates = self.strategy.signals.index[sell_signals]
                    sell_prices = self.data.loc[sell_dates, 'Close']
                    ax1.scatter(sell_dates, sell_prices, marker='v', color='red', s=100, label='Sell Signal', zorder=5)

            ax1.set_title(f'{self.ticker} - Trading Strategy')
            ax1.set_ylabel('Price')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # Panel 2: Technical indicators (separate from price)
            ax2 = fig.add_subplot(gs[1], sharex=ax1)
            indicators_plotted = 0

            for indicator in self.strategy_methods.indicators_for_chart:
                if indicator in self.strategy.df.columns:
                    # Plot all indicators that are not price-related
                    if indicator.lower() not in ['close', 'open', 'high', 'low', 'volume']:
                        ax2.plot(self.strategy.df.index, self.strategy.df[indicator], label=indicator)
                        indicators_plotted += 1

            # If no indicators were plotted, show a message
            if indicators_plotted == 0:
                ax2.text(0.5, 0.5, 'No indicators available for display',
                        horizontalalignment='center', verticalalignment='center',
                        transform=ax2.transAxes, fontsize=12)
                print("No indicators plotted in Panel 2")

            ax2.set_ylabel('Indicators')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            # Panel 3: Returns comparison
            ax3 = fig.add_subplot(gs[2], sharex=ax1)
            if 'returns' in self.strategy.signals.columns:
                ax3.plot(self.strategy.signals.index, self.strategy.signals['returns'].cumsum() * 100,
                        label='Buy & Hold Returns (%)', color='blue')
                ax3.plot(self.strategy.signals.index, self.strategy.signals['strategy_returns'].cumsum() * 100,
                        label='Strategy Returns (%)', color='green')
            ax3.set_ylabel('Returns (%)')
            ax3.legend()
            ax3.grid(True, alpha=0.3)

            # Panel 4: Portfolio value
            ax4 = fig.add_subplot(gs[3], sharex=ax1)
            if 'cumulative_returns' in self.strategy.signals.columns:
                initial_investment = 10000

                # Ensure cumulative returns start from 1.0 (not 0 due to NaN filling)
                bh_cumulative = self.strategy.signals['cumulative_returns'].ffill().fillna(1.0)
                strategy_cumulative = self.strategy.signals['strategy_cumulative_returns'].ffill().fillna(1.0)

                # Set first value to 1.0 to ensure proper starting point
                if len(bh_cumulative) > 0:
                    bh_cumulative.iloc[0] = 1.0
                    strategy_cumulative.iloc[0] = 1.0

                bh_values = bh_cumulative * initial_investment
                strategy_values = strategy_cumulative * initial_investment

                ax4.plot(self.strategy.signals.index, bh_values,
                        label='Buy & Hold Value ($)', color='blue')
                ax4.plot(self.strategy.signals.index, strategy_values,
                        label='Strategy Value ($)', color='green')

                # Set y-axis to start from a reasonable minimum (not 0)
                min_value = min(bh_values.min(), strategy_values.min())
                max_value = max(bh_values.max(), strategy_values.max())
                padding = (max_value - min_value) * 0.05  # 5% padding
                ax4.set_ylim(min_value - padding, max_value + padding)

            ax4.set_ylabel('Portfolio Value ($)')
            ax4.legend()
            ax4.grid(True, alpha=0.3)

            # Panel 5: Drawdown
            ax5 = fig.add_subplot(gs[4], sharex=ax1)
            if 'cumulative_returns' in self.strategy.signals.columns:
                # Calculate drawdowns
                bh_peak = self.strategy.signals['cumulative_returns'].cummax()
                bh_drawdown = (self.strategy.signals['cumulative_returns'] - bh_peak) / bh_peak * 100

                strat_peak = self.strategy.signals['strategy_cumulative_returns'].cummax()
                strat_drawdown = (self.strategy.signals['strategy_cumulative_returns'] - strat_peak) / strat_peak * 100

                ax5.plot(self.strategy.signals.index, bh_drawdown, label='Buy & Hold Drawdown', color='blue')
                ax5.plot(self.strategy.signals.index, strat_drawdown, label='Strategy Drawdown', color='green')
            ax5.set_ylabel('Drawdown (%)')
            ax5.set_xlabel('Date')
            ax5.legend()
            ax5.grid(True, alpha=0.3)

            # Add performance metrics
            self._add_performance_metrics(fig)

            # Adjust layout and save
            plt.tight_layout()
            plt.subplots_adjust(bottom=0.15)

            file_path = os.path.join('software/library/images', f'backtest_strategy_{self.ticker.lower()}_{self.unique_id}.png')
            plt.savefig(file_path, dpi=100, bbox_inches='tight')
            plt.close(fig)

            return file_path

        except Exception as e:
            print(f"Error generating chart: {str(e)}")
            return self._create_error_chart(str(e))

    def _add_performance_metrics(self, fig):
        """Add performance metrics text box to the chart."""
        try:
            if len(self.strategy.signals) > 0 and 'strategy_returns' in self.strategy.signals.columns:
                total_return = (self.strategy.signals['strategy_cumulative_returns'].iloc[-1] - 1) * 100
                bh_return = (self.strategy.signals['cumulative_returns'].iloc[-1] - 1) * 100

                # Calculate basic metrics
                days = (self.strategy.signals.index[-1] - self.strategy.signals.index[0]).days
                years = max(days / 365, 1)
                annual_return = ((1 + total_return/100) ** (1/years) - 1) * 100

                # Max drawdown
                peak = self.strategy.signals['strategy_cumulative_returns'].cummax()
                drawdown = (self.strategy.signals['strategy_cumulative_returns'] - peak) / peak * 100
                max_drawdown = drawdown.min()

                # Sharpe ratio (handle division by zero)
                returns_std = self.strategy.signals['strategy_returns'].std()
                if returns_std > 0:
                    sharpe = self.strategy.signals['strategy_returns'].mean() / returns_std * (252 ** 0.5)
                else:
                    sharpe = 0.0

                # Number of trades
                trades = self.strategy.signals['position'].diff().abs().sum() / 2

                metrics_text = (
                    f"Performance Metrics:\n"
                    f"Total Return: {total_return:.2f}% (Buy & Hold: {bh_return:.2f}%)\n"
                    f"Annualized Return: {annual_return:.2f}%\n"
                    f"Max Drawdown: {max_drawdown:.2f}%\n"
                    f"Sharpe Ratio: {sharpe:.2f}\n"
                    f"Number of Trades: {int(trades)}"
                )

                props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
                fig.text(0.02, 0.02, metrics_text, fontsize=9, verticalalignment='bottom', bbox=props)

        except Exception as e:
            print(f"Error calculating performance metrics: {str(e)}")

    def _create_error_chart(self, error_message: str) -> str:
        """Create a simple error chart."""
        try:
            import matplotlib.pyplot as plt

            fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, f"Error creating strategy chart:\n{error_message}",
                   horizontalalignment='center', verticalalignment='center',
                   transform=ax.transAxes, fontsize=12)
            ax.set_title(f"Strategy Analysis for {self.ticker} - Error")
            ax.axis('off')

            file_path = os.path.join('software/library/images', f'backtest_strategy_{self.ticker.lower()}_{self.unique_id}.png')
            plt.savefig(file_path, dpi=100, bbox_inches='tight')
            plt.close(fig)

            return file_path

        except Exception as e:
            print(f"Error creating error chart: {str(e)}")
            return os.path.join('software/library/images', f'backtest_strategy_{self.ticker.lower()}_{self.unique_id}.png')

async def execute_backtest_code(code: str, data: pd.DataFrame, ticker: str, unique_id: str) -> str:
    """
    Execute the backtest code with the provided data.

    Args:
        code: The Python code to execute
        data: The data to use for backtesting
        ticker: The ticker symbol
        unique_id: A unique identifier for the chart filename

    Returns:
        str: The path to the generated chart
    """
    from software.ai.graph.director_state import print_debug

    # Create directory for images if it doesn't exist
    image_dir = "software/library/images"
    os.makedirs(image_dir, exist_ok=True)

    # Create a static directory for serving images
    static_dir = "software/frontend/static"
    os.makedirs(static_dir, exist_ok=True)

    # Default chart path in case of errors
    default_chart_path = os.path.join(image_dir, f"backtest_strategy_{ticker.lower()}_{unique_id}.png")

    # Validate input data
    if data is None or data.empty:
        logger.warning(f"No data available for ticker {ticker}")
        # Create a simple error chart
        try:
            import matplotlib.pyplot as plt
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, f"No data available for ticker {ticker}",
                   horizontalalignment='center', verticalalignment='center',
                   transform=ax.transAxes, fontsize=14)
            ax.set_title(f"Strategy Analysis for {ticker} - Error")
            ax.axis('off')

            # Save the chart
            plt.savefig(default_chart_path, dpi=100, bbox_inches='tight')
            plt.close(fig)

            # Copy to static directory
            static_path = os.path.join(static_dir, f"backtest_strategy_{ticker.lower()}_{unique_id}.png")
            import shutil
            shutil.copy2(default_chart_path, static_path)

            return default_chart_path
        except Exception as e:
            logger.error(f"Error creating error chart: {str(e)}")
            return default_chart_path

    # Debug matplotlib version and available modules
    try:
        import matplotlib
        print_debug(f"Matplotlib version: {matplotlib.__version__}", "Matplotlib Version")
        print_debug(f"Matplotlib path: {matplotlib.__path__}", "Matplotlib Path")

        # Check if GridSpec is available
        if hasattr(matplotlib, 'gridspec'):
            print_debug(f"GridSpec module exists: {dir(matplotlib.gridspec)}", "GridSpec Module")
        else:
            print_debug("GridSpec module does not exist in matplotlib", "GridSpec Missing")

        # Check if matplotlib.pyplot has GridSpec
        import matplotlib.pyplot as plt
        if hasattr(plt, 'GridSpec'):
            print_debug("GridSpec exists in pyplot", "GridSpec in pyplot")
        else:
            print_debug("GridSpec does not exist in pyplot", "GridSpec Missing in pyplot")

        # Check mplfinance availability
        try:
            import mplfinance as mpf
            print_debug(f"mplfinance version: {mpf.__version__}", "mplfinance Version")
        except ImportError:
            print_debug("mplfinance not installed", "mplfinance Missing")
            # We don't auto-install packages, but we'll note this for the user
        except Exception as e:
            print_debug(f"Error checking mplfinance: {str(e)}", "mplfinance Error")
    except Exception as e:
        print_debug(f"Error checking matplotlib: {str(e)}", "Matplotlib Error")

    # Create a namespace for execution
    try:
        # Try to import GridSpec directly
        from matplotlib.gridspec import GridSpec
        print_debug("Successfully imported GridSpec directly", "GridSpec Import")

        namespace = {
            "pd": pd,
            "plt": __import__("matplotlib.pyplot").pyplot,
            "np": np,
            "uuid": uuid,
            "mpf": __import__("mplfinance"),
            "os": os,
            "df": data.copy(),  # Pass a copy of the data as df to prevent modifications
            "data": data.copy(),  # Also pass as data for flexibility
            "ticker": ticker,
            "unique_id": unique_id,
            "mdates": __import__("matplotlib.dates"),  # Add mdates
            "GridSpec": GridSpec  # Add GridSpec class directly
        }
    except ImportError as e:
        print_debug(f"Error importing GridSpec directly: {str(e)}", "GridSpec Import Error")

        # Try alternative import approach
        try:
            import matplotlib.gridspec
            print_debug(f"Successfully imported matplotlib.gridspec module", "GridSpec Module Import")

            namespace = {
                "pd": pd,
                "plt": __import__("matplotlib.pyplot").pyplot,
                "np": np,
                "uuid": uuid,
                "mpf": __import__("mplfinance"),
                "os": os,
                "df": data.copy(),  # Pass a copy of the data as df to prevent modifications
                "data": data.copy(),  # Also pass as data for flexibility
                "ticker": ticker,
                "unique_id": unique_id,
                "mdates": __import__("matplotlib.dates"),  # Add mdates
                "gridspec": matplotlib.gridspec,  # Add the full gridspec module
                "GridSpec": matplotlib.gridspec.GridSpec  # Add GridSpec class
            }
        except Exception as e:
            print_debug(f"Error with alternative GridSpec import: {str(e)}", "Alternative Import Error")

            # Fallback to basic namespace without GridSpec
            namespace = {
                "pd": pd,
                "plt": __import__("matplotlib.pyplot").pyplot,
                "np": np,
                "uuid": uuid,
                "mpf": __import__("mplfinance"),
                "os": os,
                "df": data.copy(),  # Pass a copy of the data as df to prevent modifications
                "data": data.copy(),  # Also pass as data for flexibility
                "ticker": ticker,
                "unique_id": unique_id,
                "mdates": __import__("matplotlib.dates")  # Add mdates
            }

    # Capture stdout to get any print output
    old_stdout = sys.stdout
    redirected_output = StringIO()
    sys.stdout = redirected_output

    chart_path = None

    # Modify the code to handle various import and execution issues

    # 1. Handle GridSpec import issues
    if "from matplotlib.gridspec import GridSpec" in code or "import matplotlib.gridspec" in code:
        print_debug("Code contains GridSpec import, modifying to use alternative approach", "Code Modification")
        # Add a fallback import at the beginning of the code
        modified_code = """
# Fallback GridSpec implementation
try:
    from matplotlib.gridspec import GridSpec
except ImportError:
    try:
        import matplotlib.gridspec
        GridSpec = matplotlib.gridspec.GridSpec
    except ImportError:
        # Create a simple GridSpec replacement that won't break execution
        class GridSpec:
            def __init__(self, nrows, ncols, **kwargs):
                self.nrows = nrows
                self.ncols = ncols
                self.kwargs = kwargs

            def __getitem__(self, key):
                return key
"""
        code = modified_code + code
        print_debug("Code modified with GridSpec fallback", "Code Modification")

    # 2. Handle Panel subtitle issue
    if "Panel(" in code:
        print_debug("Code contains Panel usage, adding safety wrapper", "Code Modification")
        # Add a safety wrapper for Panel creation
        panel_safety_code = """
# Safe Panel creation to handle subtitle issues
import rich
original_panel = rich.panel.Panel

def safe_panel(*args, **kwargs):
    # Handle subtitle=None case
    if 'subtitle' in kwargs and kwargs['subtitle'] is None:
        kwargs['subtitle'] = ''
    return original_panel(*args, **kwargs)

rich.panel.Panel = safe_panel
"""
        code = panel_safety_code + code
        print_debug("Code modified with Panel safety wrapper", "Code Modification")

    try:
        # Execute the code with timeout protection
        try:
            # Execute the code
            print_debug("Executing code...", "Code Execution")
            exec(code, namespace)
            print_debug("Code execution completed", "Code Execution")
        except Exception as e:
            logger.error(f"Error executing code: {str(e)}")
            logger.error(traceback.format_exc())
            print_debug(f"Error executing code: {str(e)}\n{traceback.format_exc()}", "Code Execution Error")

            # Create a simple error chart
            try:
                print_debug(f"Error details: {str(e)}\n{traceback.format_exc()}", "Detailed Error")

                # Check if the error is related to 'subtitle' attribute
                if "'NoneType' object has no attribute 'subtitle'" in str(e):
                    print_debug("Found subtitle error - this is likely due to an issue with Panel creation", "Subtitle Error")

                import matplotlib.pyplot as plt
                fig, ax = plt.subplots(figsize=(10, 6))
                ax.text(0.5, 0.5, f"Error executing code: {str(e)}",
                       horizontalalignment='center', verticalalignment='center',
                       transform=ax.transAxes, fontsize=12)
                ax.set_title(f"Strategy Analysis for {ticker} - Error")
                ax.axis('off')

                # Save the chart
                plt.savefig(default_chart_path, dpi=100, bbox_inches='tight')
                plt.close(fig)

                # Copy to static directory
                static_path = os.path.join(static_dir, f"backtest_strategy_{ticker.lower()}_{unique_id}.png")
                import shutil
                shutil.copy2(default_chart_path, static_path)

                return default_chart_path
            except Exception as chart_error:
                logger.error(f"Error creating error chart: {str(chart_error)}")
                return default_chart_path

        # Check if create_chart function was defined
        if "create_chart" in namespace:
            try:
                # Add a specific fix for the subtitle error if it occurs during execution
                try:
                    import rich.panel
                    original_panel = rich.panel.Panel

                    def safe_panel(*args, **kwargs):
                        # Handle subtitle=None case
                        if 'subtitle' in kwargs and kwargs['subtitle'] is None:
                            kwargs['subtitle'] = ''
                        return original_panel(*args, **kwargs)

                    rich.panel.Panel = safe_panel
                    print_debug("Applied runtime Panel subtitle fix", "Runtime Fix")
                except Exception as panel_error:
                    print_debug(f"Error applying Panel subtitle fix: {str(panel_error)}", "Runtime Fix Error")

                # Call the create_chart function with the data
                print_debug("Calling create_chart function", "Function Call")
                result = namespace["create_chart"](data)
                print_debug(f"create_chart result: {result}", "Function Result")

                if result and isinstance(result, str):
                    chart_path = result

                    # Check if the file exists
                    if os.path.exists(chart_path):
                        # Copy the file to the static directory for serving
                        static_path = os.path.join(static_dir, f"backtest_strategy_{ticker.lower()}_{unique_id}.png")
                        import shutil
                        shutil.copy2(chart_path, static_path)

                        # Return the path to the chart
                        return chart_path
                    else:
                        # Try with absolute path
                        abs_path = os.path.abspath(chart_path)
                        if os.path.exists(abs_path):
                            # Copy the file to the static directory for serving
                            static_path = os.path.join(static_dir, f"backtest_strategy_{ticker.lower()}_{unique_id}.png")
                            import shutil
                            shutil.copy2(abs_path, static_path)

                            # Return the path to the chart
                            return abs_path
                        else:
                            logger.warning(f"Chart file not found at path: {chart_path} or {abs_path}")
                            # Create a simple error chart
                            try:
                                import matplotlib.pyplot as plt
                                fig, ax = plt.subplots(figsize=(10, 6))
                                ax.text(0.5, 0.5, f"Chart file not found at path: {chart_path}",
                                       horizontalalignment='center', verticalalignment='center',
                                       transform=ax.transAxes, fontsize=12)
                                ax.set_title(f"Strategy Analysis for {ticker} - Error")
                                ax.axis('off')

                                # Save the chart
                                plt.savefig(default_chart_path, dpi=100, bbox_inches='tight')
                                plt.close(fig)

                                # Copy to static directory
                                static_path = os.path.join(static_dir, f"backtest_strategy_{ticker.lower()}_{unique_id}.png")
                                import shutil
                                shutil.copy2(default_chart_path, static_path)

                                return default_chart_path
                            except Exception as chart_error:
                                logger.error(f"Error creating error chart: {str(chart_error)}")
                                return default_chart_path
                else:
                    logger.warning(f"create_chart function returned invalid result: {result}")
                    # Create a simple error chart
                    try:
                        import matplotlib.pyplot as plt
                        fig, ax = plt.subplots(figsize=(10, 6))
                        ax.text(0.5, 0.5, f"Invalid chart result: {result}",
                               horizontalalignment='center', verticalalignment='center',
                               transform=ax.transAxes, fontsize=12)
                        ax.set_title(f"Strategy Analysis for {ticker} - Error")
                        ax.axis('off')

                        # Save the chart
                        plt.savefig(default_chart_path, dpi=100, bbox_inches='tight')
                        plt.close(fig)

                        # Copy to static directory
                        static_path = os.path.join(static_dir, f"backtest_strategy_{ticker.lower()}_{unique_id}.png")
                        import shutil
                        shutil.copy2(default_chart_path, static_path)

                        return default_chart_path
                    except Exception as chart_error:
                        logger.error(f"Error creating error chart: {str(chart_error)}")
                        return default_chart_path
            except Exception as e:
                logger.error(f"Error calling create_chart function: {str(e)}")
                logger.error(traceback.format_exc())
                # Create a simple error chart
                try:
                    import matplotlib.pyplot as plt
                    fig, ax = plt.subplots(figsize=(10, 6))
                    ax.text(0.5, 0.5, f"Error calling create_chart: {str(e)}",
                           horizontalalignment='center', verticalalignment='center',
                           transform=ax.transAxes, fontsize=12)
                    ax.set_title(f"Strategy Analysis for {ticker} - Error")
                    ax.axis('off')

                    # Save the chart
                    plt.savefig(default_chart_path, dpi=100, bbox_inches='tight')
                    plt.close(fig)

                    # Copy to static directory
                    static_path = os.path.join(static_dir, f"backtest_strategy_{ticker.lower()}_{unique_id}.png")
                    import shutil
                    shutil.copy2(default_chart_path, static_path)

                    return default_chart_path
                except Exception as chart_error:
                    logger.error(f"Error creating error chart: {str(chart_error)}")
                    return default_chart_path
        else:
            logger.warning("No create_chart function found in the code")
            # Create a simple error chart
            try:
                import matplotlib.pyplot as plt
                fig, ax = plt.subplots(figsize=(10, 6))
                ax.text(0.5, 0.5, "No create_chart function found in the code",
                       horizontalalignment='center', verticalalignment='center',
                       transform=ax.transAxes, fontsize=12)
                ax.set_title(f"Strategy Analysis for {ticker} - Error")
                ax.axis('off')

                # Save the chart
                plt.savefig(default_chart_path, dpi=100, bbox_inches='tight')
                plt.close(fig)

                # Copy to static directory
                static_path = os.path.join(static_dir, f"backtest_strategy_{ticker.lower()}_{unique_id}.png")
                import shutil
                shutil.copy2(default_chart_path, static_path)

                return default_chart_path
            except Exception as chart_error:
                logger.error(f"Error creating error chart: {str(chart_error)}")
                return default_chart_path
    except Exception as e:
        logger.error(f"Unexpected error executing backtest code: {str(e)}")
        logger.error(traceback.format_exc())
        # Create a simple error chart as a last resort
        try:
            import matplotlib.pyplot as plt
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, f"Unexpected error: {str(e)}",
                   horizontalalignment='center', verticalalignment='center',
                   transform=ax.transAxes, fontsize=12)
            ax.set_title(f"Strategy Analysis - Critical Error")
            ax.axis('off')

            # Save the chart
            plt.savefig(default_chart_path, dpi=100, bbox_inches='tight')
            plt.close(fig)

            # Copy to static directory
            static_path = os.path.join(static_dir, f"backtest_strategy_{ticker.lower()}_{unique_id}.png")
            import shutil
            shutil.copy2(default_chart_path, static_path)

            return default_chart_path
        except Exception as chart_error:
            logger.error(f"Error creating error chart: {str(chart_error)}")
            return default_chart_path
    finally:
        # Restore stdout
        sys.stdout = old_stdout

        # Log any output from the executed code
        output = redirected_output.getvalue()
        if output:
            logger.info(f"Output from executed code: {output}")

        # If we get here and no return statement was executed, something went wrong
        if chart_path is None:
            logger.error("Failed to generate chart but no exception was raised")
            return default_chart_path

async def run_hyperparameter_optimization(
    strategy_methods: OptimizedStrategyMethods,
    data: pd.DataFrame,
    ticker: str,
    unique_id: str,
    task_id: str
) -> OptimizationResult:
    """
    Run production-ready hyperparameter optimization with risk constraints and out-of-sample validation.

    Args:
        strategy_methods: Strategy methods with optimization configuration
        data: Historical data for backtesting
        ticker: Stock ticker symbol
        unique_id: Unique identifier for this run
        task_id: Task ID for progress updates

    Returns:
        OptimizationResult: Results of the optimization process
    """
    from software.ai.graph.director_state import print_debug

    # Generate parameter combinations
    param_combinations = generate_parameter_combinations(strategy_methods.optimization_config)

    if not param_combinations:
        return OptimizationResult(
            best_parameters={},
            best_return=0.0,
            best_sharpe=0.0,
            best_composite_score=0.0,
            best_max_drawdown=0.0,
            best_total_trades=0,
            in_sample_return=0.0,
            out_of_sample_return=0.0,
            total_combinations_tested=0,
            valid_combinations=0,
            optimization_summary="No optimization parameters configured"
        )

    # Split data for in-sample optimization and out-of-sample validation (70/30 split)
    split_point = int(len(data) * 0.7)
    in_sample_data = data.iloc[:split_point].copy()
    out_of_sample_data = data.iloc[split_point:].copy()

    print_debug(f"📊 Data Split: {len(in_sample_data)} in-sample, {len(out_of_sample_data)} out-of-sample", "Optimization")

    # Get original strategy performance for comparison (on full data)
    original_performance = run_single_backtest(strategy_methods, data)
    original_return = original_performance['total_return'] if original_performance else 0.0
    original_sharpe = original_performance['sharpe_ratio'] if original_performance else 0.0

    # Extract default parameters for display
    default_params = {}
    for indicator in strategy_methods.optimization_config.indicators:
        default_params[indicator.name] = indicator.base_value
    for threshold in strategy_methods.optimization_config.thresholds:
        default_params[threshold.name] = threshold.base_value

    # Build range information for display
    range_info = []
    for indicator in strategy_methods.optimization_config.indicators:
        range_info.append(f"{indicator.name}: [{indicator.min_value}-{indicator.max_value}] step={indicator.step}")
    for threshold in strategy_methods.optimization_config.thresholds:
        range_info.append(f"{threshold.name}: [{threshold.min_value}-{threshold.max_value}] step={threshold.step}")

    print_debug(f"🎯 OPTIMIZATION STARTED for {ticker}", "Optimization")
    print_debug(f"📊 Default Strategy: {original_return:.2f}% return, {original_sharpe:.2f} Sharpe", "Optimization")
    print_debug(f"⚙️  Default Parameters: {default_params}", "Optimization")
    print_debug(f"📏 Parameter Ranges: {'; '.join(range_info)}", "Optimization")
    print_debug(f"🔍 Testing {len(param_combinations)} parameter combinations...", "Optimization")

    # Generate replacement patterns ONCE using LLM
    print_debug(f"🤖 Generating replacement patterns with AI...", "Optimization")
    replacement_patterns = await generate_replacement_patterns(strategy_methods)
    print_debug(f"✅ Generated patterns for {len(replacement_patterns)} parameters", "Optimization")

    # Risk constraints for production trading
    MAX_DRAWDOWN_THRESHOLD = -50.0  # Reject strategies with >50% drawdown (relaxed for development)
    MIN_TRADES_THRESHOLD = 1        # Require minimum 1 trade (relaxed for signal generation)
    MIN_SHARPE_THRESHOLD = -2.0     # Allow negative Sharpe (relaxed for development)

    best_composite_score = float('-inf')
    best_return = float('-inf')
    best_sharpe = float('-inf')
    best_params = {}
    best_max_drawdown = 0.0
    best_total_trades = 0
    best_in_sample_return = 0.0
    best_out_of_sample_return = 0.0
    results = []
    valid_combinations = 0

    for i, params in enumerate(param_combinations):
        try:
            # Update progress
            progress = 85 + int((i / len(param_combinations)) * 10)  # 85-95%
            tasks[task_id]["progress"] = progress
            tasks[task_id]["message"] = f"Optimizing parameters: {i+1}/{len(param_combinations)}"

            # Create modified strategy with current parameters
            modified_strategy = create_modified_strategy(strategy_methods, params, replacement_patterns)

            # Run backtest on in-sample data for optimization
            in_sample_performance = run_enhanced_backtest(modified_strategy, in_sample_data)

            if not in_sample_performance:
                continue

            # Apply risk constraints
            if (in_sample_performance['max_drawdown'] < MAX_DRAWDOWN_THRESHOLD or
                in_sample_performance['total_trades'] < MIN_TRADES_THRESHOLD or
                in_sample_performance['sharpe_ratio'] < MIN_SHARPE_THRESHOLD):
                continue

            valid_combinations += 1

            # Run out-of-sample validation
            out_of_sample_performance = run_enhanced_backtest(modified_strategy, out_of_sample_data)
            out_of_sample_return = out_of_sample_performance['total_return'] if out_of_sample_performance else 0.0

            # Calculate composite score (60% return, 40% Sharpe ratio)
            composite_score = (in_sample_performance['total_return'] * 0.6) + (in_sample_performance['sharpe_ratio'] * 40)

            results.append({
                'params': params,
                'in_sample_return': in_sample_performance['total_return'],
                'out_of_sample_return': out_of_sample_return,
                'sharpe': in_sample_performance['sharpe_ratio'],
                'max_drawdown': in_sample_performance['max_drawdown'],
                'total_trades': in_sample_performance['total_trades'],
                'composite_score': composite_score
            })

            # Check if this is the best result based on composite score
            if composite_score > best_composite_score:
                best_composite_score = composite_score
                best_return = in_sample_performance['total_return']
                best_sharpe = in_sample_performance['sharpe_ratio']
                best_max_drawdown = in_sample_performance['max_drawdown']
                best_total_trades = in_sample_performance['total_trades']
                best_in_sample_return = in_sample_performance['total_return']
                best_out_of_sample_return = out_of_sample_return
                best_params = params.copy()

        except Exception as e:
            continue

    print_debug(f"✅ Valid Combinations: {valid_combinations}/{len(param_combinations)} passed risk constraints", "Optimization")
    print_debug(f"🔬 Tested {len(param_combinations)} combinations", "Optimization")

    # Handle case where no valid combinations found - fall back to default strategy
    if valid_combinations == 0:
        print_debug(f"❌ NO VALID COMBINATIONS FOUND - Falling back to default strategy", "Optimization")

        # Calculate default strategy performance
        default_params = {}
        for indicator in strategy_methods.optimization_config.indicators:
            default_params[indicator.name] = indicator.base_value
        for threshold in strategy_methods.optimization_config.thresholds:
            default_params[threshold.name] = threshold.base_value

        # Test default strategy performance
        try:
            replacement_patterns = await generate_replacement_patterns(strategy_methods)
            default_strategy_methods = create_modified_strategy(strategy_methods, default_params, replacement_patterns)
            default_template = BacktestTemplate(in_sample_data, default_strategy_methods, ticker, f"{unique_id}_default")
            default_strategy = default_template._create_strategy_instance()
            default_performance = calculate_comprehensive_metrics(default_strategy)

            default_return = default_performance.get('total_return_pct', 0.0)
            default_sharpe = default_performance.get('sharpe_ratio', 0.0)
            default_drawdown = abs(default_performance.get('max_drawdown', 0.0))
            default_trades = default_performance.get('total_trades', 0)

            print_debug(f"🏆 OPTIMIZATION COMPLETE!", "Optimization")
            print_debug(f"🎯 Default strategy: {default_return:.2f}% return, {default_sharpe:.2f} Sharpe, {default_trades} trades", "Optimization")

            return OptimizationResult(
                best_parameters=default_params,
                best_return=default_return,
                best_sharpe=default_sharpe,
                best_composite_score=default_return * 0.6 + default_sharpe * 40,  # Same scoring as optimization
                best_max_drawdown=default_drawdown,
                best_total_trades=default_trades,
                in_sample_return=default_return,
                out_of_sample_return=default_return,  # Use same value since no optimization occurred
                total_combinations_tested=len(param_combinations),
                valid_combinations=0,
                optimization_summary=f"Tested {len(param_combinations)} combinations. No strategies passed risk constraints. Default strategy is optimal: {default_return:.2f}% return, {default_sharpe:.2f} Sharpe."
            )
        except Exception as e:
            print_debug(f"❌ Failed to calculate default strategy performance: {str(e)}", "Optimization")
            return OptimizationResult(
                best_parameters={},
                best_return=0.0,
                best_sharpe=0.0,
                best_composite_score=0.0,
                best_max_drawdown=0.0,
                best_total_trades=0,
                in_sample_return=0.0,
                out_of_sample_return=0.0,
                total_combinations_tested=len(param_combinations),
                valid_combinations=0,
                optimization_summary=f"Tested {len(param_combinations)} combinations. No strategies passed risk constraints and failed to evaluate default strategy."
            )

    # Valid combinations found - print optimization results
    return_improvement = best_return - original_return
    sharpe_improvement = best_sharpe - original_sharpe
    improvement_pct = ((best_return / original_return) - 1) * 100 if original_return != 0 else 0

    print_debug(f"🏆 OPTIMIZATION COMPLETE!", "Optimization")
    print_debug(f"🎉 Winner Strategy: {best_return:.2f}% return, {best_sharpe:.2f} Sharpe", "Optimization")
    print_debug(f"🚀 Best Parameters: {best_params}", "Optimization")
    print_debug(f"📈 Improvement: +{return_improvement:.2f}% return (+{improvement_pct:.2f}%), +{sharpe_improvement:.2f} Sharpe", "Optimization")
    print_debug(f"🛡️  Risk Metrics: {best_max_drawdown:.2f}% max drawdown, {best_total_trades} trades", "Optimization")
    print_debug(f"📊 Out-of-Sample: {best_out_of_sample_return:.2f}% return", "Optimization")

    optimization_summary = f"Tested {len(param_combinations)} combinations ({valid_combinations} valid). Best composite score: {best_composite_score:.2f} (Return: {best_return:.2f}%, Sharpe: {best_sharpe:.2f}, Out-of-sample: {best_out_of_sample_return:.2f}%)"

    return OptimizationResult(
        best_parameters=best_params,
        best_return=best_return,
        best_sharpe=best_sharpe,
        best_composite_score=best_composite_score,
        best_max_drawdown=best_max_drawdown,
        best_total_trades=best_total_trades,
        in_sample_return=best_in_sample_return,
        out_of_sample_return=best_out_of_sample_return,
        total_combinations_tested=len(param_combinations),
        valid_combinations=valid_combinations,
        optimization_summary=optimization_summary
    )

def generate_parameter_combinations(config: OptimizationConfig) -> List[Dict[str, Any]]:
    """Generate all parameter combinations for optimization."""

    param_ranges = {}

    # Add indicator parameter ranges (handle both int and float)
    for indicator in config.indicators:
        values = []
        current = indicator.min_value
        while current <= indicator.max_value:
            values.append(current)
            current += indicator.step
        param_ranges[indicator.name] = values

    # Add threshold parameter ranges
    for threshold in config.thresholds:
        values = []
        current = threshold.min_value
        while current <= threshold.max_value:
            values.append(current)
            current += threshold.step
        param_ranges[threshold.name] = values

    if not param_ranges:
        return []

    # Generate all combinations using itertools.product
    param_names = list(param_ranges.keys())
    param_values = list(param_ranges.values())

    combinations = []
    for combo in product(*param_values):
        param_dict = dict(zip(param_names, combo))
        combinations.append(param_dict)

        # Limit combinations to prevent excessive computation
        if len(combinations) >= config.max_combinations:
            break

    return combinations

def create_modified_strategy(base_strategy: OptimizedStrategyMethods, params: Dict[str, Any], patterns: Dict[str, Dict[str, str]]) -> OptimizedStrategyMethods:
    """Create a modified strategy with new parameters using pre-generated patterns."""

    # Create a copy of the base strategy
    modified_strategy = OptimizedStrategyMethods(
        calculate_indicators_code=base_strategy.calculate_indicators_code,
        should_long_code=base_strategy.should_long_code,
        should_exit_long_code=base_strategy.should_exit_long_code,
        should_short_code=base_strategy.should_short_code,
        should_exit_short_code=base_strategy.should_exit_short_code,
        indicators_for_chart=base_strategy.indicators_for_chart,

        optimization_config=base_strategy.optimization_config
    )

    # Replace parameter values in the code using patterns
    for param_name, param_value in params.items():
        # Replace in calculate_indicators_code
        modified_strategy.calculate_indicators_code = apply_replacement_patterns(
            modified_strategy.calculate_indicators_code, param_name, param_value, patterns
        )

        # Replace in entry/exit logic
        modified_strategy.should_long_code = apply_replacement_patterns(
            modified_strategy.should_long_code, param_name, param_value, patterns
        )

        modified_strategy.should_exit_long_code = apply_replacement_patterns(
            modified_strategy.should_exit_long_code, param_name, param_value, patterns
        )

    return modified_strategy

async def generate_replacement_patterns(strategy_methods: OptimizedStrategyMethods) -> Dict[str, Dict[str, str]]:
    """Generate replacement patterns for all parameters using LLM once."""

    try:
        from software.ai.llm.llm_connect import get_llm_connect
        from langchain_core.messages import SystemMessage, HumanMessage

        # Use AI to generate replacement patterns
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()
        # Uncomment to use specific LLM
        # model = await llm_connect.get_llm_by_id("67f972e51c64621068267398")

        # Collect all parameter names from optimization config
        param_names = []
        for indicator in strategy_methods.optimization_config.indicators:
            param_names.append(indicator.name)
        for threshold in strategy_methods.optimization_config.thresholds:
            param_names.append(threshold.name)

        system_prompt = f"""You are a code parameter replacement pattern generator. Analyze the given trading strategy code and generate regex replacement patterns for each parameter.

TASK: For each parameter name, identify what patterns need to be replaced in the code.

PARAMETERS TO ANALYZE: {', '.join(param_names)}

CODE SECTIONS:
1. Calculate Indicators: {strategy_methods.calculate_indicators_code}
2. Entry Logic: {strategy_methods.should_long_code}
3. Exit Logic: {strategy_methods.should_exit_long_code}

RULES:
1. For each parameter, find ALL patterns that need replacement
2. Return a JSON object with parameter names as keys
3. Each parameter should have an array of [old_pattern, new_pattern_template] pairs
4. Use {{value}} as placeholder for the new parameter value
5. Include regex patterns for complex replacements

EXAMPLE OUTPUT:
{{
    "sma_20_period": [
        ["rolling\\(20\\)", "rolling({{value}})"],
        ["sma_20", "sma_{{value}}"]
    ],
    "oversold_threshold": [
        ["< 20\\.0", "< {{value}}"],
        ["< 20", "< {{value}}"]
    ]
}}

Return only valid JSON without any explanations."""

        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content="Generate replacement patterns for the parameters.")
        ]

        response = await model.ainvoke(messages)
        patterns_text = response.content.strip()

        # Remove any markdown formatting
        if patterns_text.startswith('```json'):
            patterns_text = patterns_text.replace('```json', '').replace('```', '').strip()
        elif patterns_text.startswith('```'):
            patterns_text = patterns_text.replace('```', '').strip()

        # Parse JSON response
        import json
        patterns = json.loads(patterns_text)
        return patterns

    except Exception as e:
        # Fallback to empty patterns
        return {}

def apply_replacement_patterns(code: str, param_name: str, param_value: Any, patterns: Dict[str, Dict[str, str]]) -> str:
    """Apply pre-generated replacement patterns efficiently."""
    import re

    if param_name not in patterns:
        return code

    modified_code = code
    for old_pattern, new_pattern_template in patterns[param_name]:
        new_pattern = new_pattern_template.replace('{value}', str(param_value))
        modified_code = re.sub(old_pattern, new_pattern, modified_code)

    return modified_code

def run_single_backtest(strategy_methods: OptimizedStrategyMethods, data: pd.DataFrame) -> Optional[Dict[str, float]]:
    """Run a single backtest and return basic performance metrics."""

    try:
        # Create a temporary strategy instance
        template = BacktestTemplate(data, strategy_methods, "TEMP", "opt")
        strategy = template._create_strategy_instance()

        if not hasattr(strategy, 'signals') or strategy.signals.empty:
            return None

        # Calculate performance metrics
        if 'strategy_returns' not in strategy.signals.columns:
            return None

        total_return = (strategy.signals['strategy_cumulative_returns'].iloc[-1] - 1) * 100

        # Calculate Sharpe ratio
        returns_std = strategy.signals['strategy_returns'].std()
        if returns_std > 0:
            sharpe_ratio = strategy.signals['strategy_returns'].mean() / returns_std * (252 ** 0.5)
        else:
            sharpe_ratio = 0.0

        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio
        }

    except Exception as e:
        return None

def run_enhanced_backtest(strategy_methods: OptimizedStrategyMethods, data: pd.DataFrame) -> Optional[Dict[str, float]]:
    """Run enhanced backtest with comprehensive risk metrics for optimization."""

    try:
        # Create a temporary strategy instance
        template = BacktestTemplate(data, strategy_methods, "TEMP", "opt")
        strategy = template._create_strategy_instance()

        if not hasattr(strategy, 'signals') or strategy.signals.empty:
            return None

        # Calculate performance metrics
        if 'strategy_returns' not in strategy.signals.columns:
            return None

        total_return = (strategy.signals['strategy_cumulative_returns'].iloc[-1] - 1) * 100

        # Calculate Sharpe ratio
        returns_std = strategy.signals['strategy_returns'].std()
        if returns_std > 0:
            sharpe_ratio = strategy.signals['strategy_returns'].mean() / returns_std * (252 ** 0.5)
        else:
            sharpe_ratio = 0.0

        # Calculate max drawdown
        cumulative = strategy.signals['strategy_cumulative_returns']
        peak = cumulative.cummax()
        drawdown = (cumulative - peak) / peak
        max_drawdown = drawdown.min() * 100

        # Count total trades
        position_changes = strategy.signals['position'].diff()
        total_trades = (position_changes > 0).sum()  # Count buy signals

        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades
        }

    except Exception as e:
        return None

async def create_optimized_chart(
    strategy_methods: OptimizedStrategyMethods,
    data: pd.DataFrame,
    ticker: str,
    unique_id: str,
    optimization_result: OptimizationResult
) -> Optional[str]:
    """Create a chart showing both original and optimized strategy performance."""

    try:
        # Create original strategy
        original_template = BacktestTemplate(data, strategy_methods, ticker, unique_id)
        original_strategy = original_template._create_strategy_instance()

        # Check if optimization actually found better parameters
        if optimization_result.valid_combinations == 0:
            # No optimization occurred - use original strategy for both
            optimized_strategy = original_strategy
        else:
            # Generate replacement patterns and create optimized strategy
            replacement_patterns = await generate_replacement_patterns(strategy_methods)
            optimized_strategy_methods = create_modified_strategy(strategy_methods, optimization_result.best_parameters, replacement_patterns)
            optimized_template = BacktestTemplate(data, optimized_strategy_methods, ticker, f"{unique_id}_opt")
            optimized_strategy = optimized_template._create_strategy_instance()

        # Generate enhanced chart with both strategies
        chart_path = generate_comparison_chart(
            original_strategy, optimized_strategy, ticker, unique_id, optimization_result.valid_combinations > 0
        )

        return chart_path

    except Exception as e:
        return None

def generate_comparison_chart(
    original_strategy,
    optimized_strategy,
    ticker: str,
    unique_id: str,
    optimization_occurred: bool = True
) -> str:
    """Generate a comparison chart showing original vs optimized strategy."""

    try:
        import matplotlib.pyplot as plt
        from matplotlib.gridspec import GridSpec

        # Create figure and subplots
        fig = plt.figure(figsize=(14, 12))
        gs = GridSpec(5, 1, height_ratios=[2, 1, 1, 1, 1], figure=fig)

        # Panel 1: Price chart with signals (both strategies)
        ax1 = fig.add_subplot(gs[0])
        ax1.plot(original_strategy.df.index, original_strategy.df['Close'], label='Price', color='black', linewidth=1)

        # Add original strategy signals
        if 'position' in original_strategy.signals.columns:
            buy_signals = original_strategy.signals['position'].diff() > 0
            sell_signals = original_strategy.signals['position'].diff() < 0

            if buy_signals.any():
                buy_dates = original_strategy.signals.index[buy_signals]
                buy_prices = original_strategy.df.loc[buy_dates, 'Close']
                ax1.scatter(buy_dates, buy_prices, marker='^', color='green', s=100, label='Original Buy', zorder=5)

            if sell_signals.any():
                sell_dates = original_strategy.signals.index[sell_signals]
                sell_prices = original_strategy.df.loc[sell_dates, 'Close']
                ax1.scatter(sell_dates, sell_prices, marker='v', color='red', s=100, label='Original Sell', zorder=5)

        # Add optimized strategy signals
        if 'position' in optimized_strategy.signals.columns:
            opt_buy_signals = optimized_strategy.signals['position'].diff() > 0
            opt_sell_signals = optimized_strategy.signals['position'].diff() < 0

            if opt_buy_signals.any():
                opt_buy_dates = optimized_strategy.signals.index[opt_buy_signals]
                opt_buy_prices = optimized_strategy.df.loc[opt_buy_dates, 'Close']
                ax1.scatter(opt_buy_dates, opt_buy_prices, marker='^', color='darkgreen', s=80, label='Optimized Buy', zorder=4)

            if opt_sell_signals.any():
                opt_sell_dates = optimized_strategy.signals.index[opt_sell_signals]
                opt_sell_prices = optimized_strategy.df.loc[opt_sell_dates, 'Close']
                ax1.scatter(opt_sell_dates, opt_sell_prices, marker='v', color='darkred', s=80, label='Optimized Sell', zorder=4)

        title_suffix = "(Original vs Optimized)" if optimization_occurred else "(Default Strategy Optimal)"
        ax1.set_title(f'{ticker} - Trading Strategy {title_suffix}')
        ax1.set_ylabel('Price')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Panel 2: Technical indicators
        ax2 = fig.add_subplot(gs[1], sharex=ax1)
        for indicator in original_strategy.strategy_methods.indicators_for_chart:
            if indicator in original_strategy.df.columns:
                ax2.plot(original_strategy.df.index, original_strategy.df[indicator], label=indicator)
        ax2.set_ylabel('Indicators')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # Panel 3: Returns comparison (now with 3 lines: Buy&Hold, Original, Optimized)
        ax3 = fig.add_subplot(gs[2], sharex=ax1)
        if 'returns' in original_strategy.signals.columns:
            ax3.plot(original_strategy.signals.index, original_strategy.signals['returns'].cumsum() * 100,
                    label='Buy & Hold Returns (%)', color='blue')
            ax3.plot(original_strategy.signals.index, original_strategy.signals['strategy_returns'].cumsum() * 100,
                    label='Original Strategy Returns (%)', color='green')

            if optimization_occurred:
                ax3.plot(optimized_strategy.signals.index, optimized_strategy.signals['strategy_returns'].cumsum() * 100,
                        label='Optimized Strategy Returns (%)', color='orange')
            else:
                # No optimization - show that default strategy is optimal
                ax3.plot(original_strategy.signals.index, original_strategy.signals['strategy_returns'].cumsum() * 100,
                        label='Default Strategy (Optimal)', color='orange', linestyle='--', alpha=0.8)
        ax3.set_ylabel('Returns (%)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # Panel 4: Portfolio value comparison
        ax4 = fig.add_subplot(gs[3], sharex=ax1)
        if 'cumulative_returns' in original_strategy.signals.columns:
            initial_investment = 10000

            # Ensure cumulative returns start from 1.0 (not 0 due to NaN filling)
            bh_cumulative = original_strategy.signals['cumulative_returns'].ffill().fillna(1.0)
            orig_cumulative = original_strategy.signals['strategy_cumulative_returns'].ffill().fillna(1.0)

            # Set first value to 1.0 to ensure proper starting point
            if len(bh_cumulative) > 0:
                bh_cumulative.iloc[0] = 1.0
                orig_cumulative.iloc[0] = 1.0

            bh_values = bh_cumulative * initial_investment
            orig_values = orig_cumulative * initial_investment

            ax4.plot(original_strategy.signals.index, bh_values,
                    label='Buy & Hold Value ($)', color='blue')
            ax4.plot(original_strategy.signals.index, orig_values,
                    label='Original Strategy Value ($)', color='green')

            if optimization_occurred:
                opt_cumulative = optimized_strategy.signals['strategy_cumulative_returns'].ffill().fillna(1.0)
                if len(opt_cumulative) > 0:
                    opt_cumulative.iloc[0] = 1.0
                opt_values = opt_cumulative * initial_investment
                ax4.plot(optimized_strategy.signals.index, opt_values,
                        label='Optimized Strategy Value ($)', color='orange')
                min_value = min(bh_values.min(), orig_values.min(), opt_values.min())
                max_value = max(bh_values.max(), orig_values.max(), opt_values.max())
            else:
                # No optimization - show that default strategy is optimal
                ax4.plot(original_strategy.signals.index, orig_values,
                        label='Default Strategy (Optimal)', color='orange', linestyle='--', alpha=0.8)
                min_value = min(bh_values.min(), orig_values.min())
                max_value = max(bh_values.max(), orig_values.max())

            # Set y-axis to start from a reasonable minimum (not 0)
            padding = (max_value - min_value) * 0.05  # 5% padding
            ax4.set_ylim(min_value - padding, max_value + padding)

        ax4.set_ylabel('Portfolio Value ($)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        # Panel 5: Drawdown comparison
        ax5 = fig.add_subplot(gs[4], sharex=ax1)
        if 'cumulative_returns' in original_strategy.signals.columns:
            # Original strategy drawdown
            orig_peak = original_strategy.signals['strategy_cumulative_returns'].cummax()
            orig_drawdown = (original_strategy.signals['strategy_cumulative_returns'] - orig_peak) / orig_peak * 100

            # Buy & Hold drawdown
            bh_peak = original_strategy.signals['cumulative_returns'].cummax()
            bh_drawdown = (original_strategy.signals['cumulative_returns'] - bh_peak) / bh_peak * 100

            ax5.plot(original_strategy.signals.index, bh_drawdown, label='Buy & Hold Drawdown', color='blue')
            ax5.plot(original_strategy.signals.index, orig_drawdown, label='Original Strategy Drawdown', color='green')

            if optimization_occurred:
                # Optimized strategy drawdown
                opt_peak = optimized_strategy.signals['strategy_cumulative_returns'].cummax()
                opt_drawdown = (optimized_strategy.signals['strategy_cumulative_returns'] - opt_peak) / opt_peak * 100
                ax5.plot(optimized_strategy.signals.index, opt_drawdown, label='Optimized Strategy Drawdown', color='orange')
            else:
                # No optimization - show that default strategy is optimal
                ax5.plot(original_strategy.signals.index, orig_drawdown, label='Default Strategy (Optimal)', color='orange', linestyle='--', alpha=0.8)

        ax5.set_ylabel('Drawdown (%)')
        ax5.set_xlabel('Date')
        ax5.legend()
        ax5.grid(True, alpha=0.3)



        # Adjust layout and save
        plt.tight_layout()

        file_path = os.path.join('software/library/images', f'backtest_strategy_{ticker.lower()}_{unique_id}.png')
        plt.savefig(file_path, dpi=100, bbox_inches='tight')
        plt.close(fig)

        # Copy to static directory
        static_dir = "software/frontend/static"
        os.makedirs(static_dir, exist_ok=True)
        static_path = os.path.join(static_dir, f"backtest_strategy_{ticker.lower()}_{unique_id}.png")
        import shutil
        shutil.copy2(file_path, static_path)

        return file_path

    except Exception as e:
        print(f"Error generating comparison chart: {str(e)}")
        return None



async def asyncio_sleep(seconds: float):
    """
    Asynchronous sleep function.

    Args:
        seconds: Number of seconds to sleep
    """
    await asyncio.sleep(seconds)
