#!/usr/bin/env python3
"""
Test script for the new strategy generation approach using fake conversation technique.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from software.api.routers.backtest_strategy import generate_raw_strategy_code, parse_strategy_code

async def test_new_approach():
    """Test the new strategy generation approach."""
    
    # Test business question
    business_question = "Backtest a strategy that buys Apple (AAPL) when the RSI is below 30 and sells when it's above 70"
    ticker = "AAPL"
    data_source = "yfinance"
    
    print("🚀 Testing new strategy generation approach...")
    print(f"📊 Business Question: {business_question}")
    print(f"🎯 Ticker: {ticker}")
    print(f"📈 Data Source: {data_source}")
    print()
    
    try:
        # Step 1: Generate raw strategy code
        print("Step 1: Generating raw strategy code using fake conversation technique...")
        raw_code = await generate_raw_strategy_code(business_question, ticker, data_source)
        
        if raw_code:
            print("✅ Raw strategy code generated successfully!")
            print("📝 Generated Code:")
            print("-" * 50)
            print(raw_code)
            print("-" * 50)
            print()
            
            # Step 2: Parse the raw code into structured format
            print("Step 2: Parsing raw code into structured format...")
            strategy_methods = await parse_strategy_code(raw_code)
            
            print("✅ Strategy code parsed successfully!")
            print("📋 Parsed Components:")
            print(f"  - Calculate Indicators: {len(strategy_methods.calculate_indicators_code)} chars")
            print(f"  - Should Long: {len(strategy_methods.should_long_code)} chars")
            print(f"  - Should Exit Long: {len(strategy_methods.should_exit_long_code)} chars")
            print(f"  - Indicators for Chart: {strategy_methods.indicators_for_chart}")
            print(f"  - Optimization Config: {len(strategy_methods.optimization_config.indicators)} indicators, {len(strategy_methods.optimization_config.thresholds)} thresholds")
            print()
            
            print("🎉 New approach test completed successfully!")
            return True
            
        else:
            print("❌ Failed to generate raw strategy code")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_new_approach())
    if success:
        print("\n✅ All tests passed! The new approach is working correctly.")
    else:
        print("\n❌ Tests failed. Please check the implementation.")
